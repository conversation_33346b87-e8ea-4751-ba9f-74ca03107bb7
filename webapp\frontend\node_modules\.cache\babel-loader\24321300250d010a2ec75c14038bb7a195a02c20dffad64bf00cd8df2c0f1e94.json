{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\cavi\\\\CertificazioneCaviImproved.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Autocomplete, CircularProgress, Alert, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, IconButton, Tabs, Tab, Pagination, InputAdornment, Divider, Stack, Chip, Tooltip, Badge } from '@mui/material';\nimport { Add as AddIcon, Search as SearchIcon, FilterList as FilterIcon, PictureAsPdf as PdfIcon, Download as DownloadIcon, Visibility as ViewIcon, Delete as DeleteIcon, Edit as EditIcon, Save as SaveIcon, Clear as ClearIcon, Build as BuildIcon, CheckCircle as CheckIcon, Warning as WarningIcon } from '@mui/icons-material';\nimport certificazioneService from '../../services/certificazioneService';\nimport caviService from '../../services/caviService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CertificazioneCaviImproved = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  cantiereId,\n  onSuccess,\n  onError\n}, ref) => {\n  _s();\n  // Stati principali\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState(0);\n  const [certificazioni, setCertificazioni] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [strumenti, setStrumenti] = useState([]);\n\n  // Stati per ricerca e filtri\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredCavi, setFilteredCavi] = useState([]);\n  const [filteredCertificazioni, setFilteredCertificazioni] = useState([]);\n  const [filters, setFilters] = useState({\n    stato: '',\n    tipologia: '',\n    operatore: ''\n  });\n\n  // Stati per paginazione\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10);\n\n  // Stati per dialogs\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedItem, setSelectedItem] = useState(null);\n\n  // Stati per form certificazione\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    id_operatore: '',\n    id_strumento: '',\n    lunghezza_misurata: '',\n    valore_continuita: 'OK',\n    valore_isolamento: '',\n    valore_resistenza: 'OK',\n    note: ''\n  });\n\n  // Carica dati iniziali\n  useEffect(() => {\n    loadInitialData();\n  }, [cantiereId]);\n\n  // Filtra cavi in base alla ricerca\n  useEffect(() => {\n    filterCavi();\n  }, [cavi, searchTerm, filters]);\n\n  // Filtra certificazioni\n  useEffect(() => {\n    filterCertificazioni();\n  }, [certificazioni, searchTerm]);\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n      await Promise.all([loadCertificazioni(), loadCavi(), loadStrumenti()]);\n    } catch (error) {\n      onError('Errore nel caricamento dei dati iniziali');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadCertificazioni = async () => {\n    try {\n      const data = await certificazioneService.getCertificazioni(cantiereId);\n      setCertificazioni(data);\n    } catch (error) {\n      console.error('Errore nel caricamento delle certificazioni:', error);\n      throw error;\n    }\n  };\n  const loadCavi = async () => {\n    try {\n      const data = await caviService.getCavi(cantiereId);\n      setCavi(data);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      throw error;\n    }\n  };\n  const loadStrumenti = async () => {\n    try {\n      const data = await certificazioneService.getStrumenti(cantiereId);\n      setStrumenti(data);\n    } catch (error) {\n      console.error('Errore nel caricamento degli strumenti:', error);\n      throw error;\n    }\n  };\n  const filterCavi = () => {\n    let filtered = cavi;\n\n    // Filtro per ricerca testuale\n    if (searchTerm) {\n      filtered = filtered.filter(cavo => {\n        var _cavo$tipologia, _cavo$ubicazione_part, _cavo$ubicazione_arri;\n        return cavo.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) || ((_cavo$tipologia = cavo.tipologia) === null || _cavo$tipologia === void 0 ? void 0 : _cavo$tipologia.toLowerCase().includes(searchTerm.toLowerCase())) || ((_cavo$ubicazione_part = cavo.ubicazione_partenza) === null || _cavo$ubicazione_part === void 0 ? void 0 : _cavo$ubicazione_part.toLowerCase().includes(searchTerm.toLowerCase())) || ((_cavo$ubicazione_arri = cavo.ubicazione_arrivo) === null || _cavo$ubicazione_arri === void 0 ? void 0 : _cavo$ubicazione_arri.toLowerCase().includes(searchTerm.toLowerCase()));\n      });\n    }\n\n    // Filtri specifici\n    if (filters.stato) {\n      filtered = filtered.filter(cavo => cavo.stato_installazione === filters.stato);\n    }\n    if (filters.tipologia) {\n      filtered = filtered.filter(cavo => cavo.tipologia === filters.tipologia);\n    }\n    setFilteredCavi(filtered);\n  };\n  const filterCertificazioni = () => {\n    let filtered = certificazioni;\n    if (searchTerm) {\n      filtered = filtered.filter(cert => {\n        var _cert$operatore, _cert$numero_certific;\n        return cert.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) || ((_cert$operatore = cert.operatore) === null || _cert$operatore === void 0 ? void 0 : _cert$operatore.toLowerCase().includes(searchTerm.toLowerCase())) || ((_cert$numero_certific = cert.numero_certificato) === null || _cert$numero_certific === void 0 ? void 0 : _cert$numero_certific.toLowerCase().includes(searchTerm.toLowerCase()));\n      });\n    }\n    if (filters.operatore) {\n      filtered = filtered.filter(cert => cert.operatore === filters.operatore);\n    }\n    setFilteredCertificazioni(filtered);\n  };\n\n  // Gestione tabs\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setCurrentPage(1);\n    setSearchTerm('');\n    setFilters({\n      stato: '',\n      tipologia: '',\n      operatore: ''\n    });\n  };\n\n  // Gestione dialogs\n  const openCreateDialog = () => {\n    setDialogType('create');\n    setSelectedItem(null);\n    setFormData({\n      id_cavo: '',\n      id_operatore: '',\n      id_strumento: '',\n      lunghezza_misurata: '',\n      valore_continuita: 'OK',\n      valore_isolamento: '',\n      valore_resistenza: 'OK',\n      note: ''\n    });\n    setOpenDialog(true);\n  };\n  const closeDialog = () => {\n    setOpenDialog(false);\n    setSelectedItem(null);\n    setDialogType('');\n  };\n\n  // Gestione form\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleCavoSelect = cavo => {\n    setFormData(prev => ({\n      ...prev,\n      id_cavo: cavo.id_cavo,\n      lunghezza_misurata: cavo.metratura_reale || cavo.metri_teorici || ''\n    }));\n  };\n\n  // Operazioni CRUD\n  const handleCreateCertificazione = async () => {\n    try {\n      if (!formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento) {\n        onError('Compila tutti i campi obbligatori');\n        return;\n      }\n      setLoading(true);\n      await certificazioneService.createCertificazione(cantiereId, formData);\n      onSuccess('Certificazione creata con successo');\n      closeDialog();\n      await loadCertificazioni();\n    } catch (error) {\n      onError('Errore nella creazione della certificazione: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleGeneratePdf = async certificazione => {\n    try {\n      setLoading(true);\n      const response = await certificazioneService.generatePdf(cantiereId, certificazione.id_certificazione);\n      if (response.file_url) {\n        window.open(response.file_url, '_blank');\n        onSuccess('PDF generato con successo');\n      } else {\n        onError('Errore nella generazione del PDF');\n      }\n    } catch (error) {\n      onError('Errore nella generazione del PDF: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteCertificazione = async certificazione => {\n    if (window.confirm(`Sei sicuro di voler eliminare la certificazione ${certificazione.numero_certificato}?`)) {\n      try {\n        setLoading(true);\n        await certificazioneService.deleteCertificazione(cantiereId, certificazione.id_certificazione);\n        onSuccess('Certificazione eliminata con successo');\n        await loadCertificazioni();\n      } catch (error) {\n        onError('Errore nell\\'eliminazione della certificazione: ' + (error.message || 'Errore sconosciuto'));\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n\n  // Espone metodi tramite ref\n  useImperativeHandle(ref, () => ({\n    handleOptionSelect: option => {\n      if (option === 'creaCertificazione') {\n        openCreateDialog();\n      } else if (option === 'visualizzaCertificazioni') {\n        setActiveTab(1);\n      }\n    }\n  }));\n\n  // Calcola elementi per paginazione\n  const getCurrentPageItems = items => {\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    return items.slice(startIndex, endIndex);\n  };\n  const getTotalPages = items => Math.ceil(items.length / itemsPerPage);\n\n  // Ottieni opzioni uniche per filtri\n  const getUniqueValues = (array, field) => {\n    return [...new Set(array.map(item => item[field]).filter(Boolean))];\n  };\n\n  // Renderizza la barra di ricerca e filtri\n  const renderSearchAndFilters = () => /*#__PURE__*/_jsxDEV(Paper, {\n    sx: {\n      p: 2,\n      mb: 2\n    },\n    children: /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      alignItems: \"center\",\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          placeholder: \"Cerca per ID cavo, tipologia, ubicazione...\",\n          value: searchTerm,\n          onChange: e => setSearchTerm(e.target.value),\n          InputProps: {\n            startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"start\",\n              children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 17\n            }, this),\n            endAdornment: searchTerm && /*#__PURE__*/_jsxDEV(InputAdornment, {\n              position: \"end\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => setSearchTerm(''),\n                children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this)\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this), activeTab === 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Stato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: filters.stato,\n              onChange: e => setFilters(prev => ({\n                ...prev,\n                stato: e.target.value\n              })),\n              label: \"Stato\",\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"Tutti\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this), getUniqueValues(cavi, 'stato_installazione').map(stato => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: stato,\n                children: stato\n              }, stato, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              children: \"Tipologia\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: filters.tipologia,\n              onChange: e => setFilters(prev => ({\n                ...prev,\n                tipologia: e.target.value\n              })),\n              label: \"Tipologia\",\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: \"Tutte\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this), getUniqueValues(cavi, 'tipologia').map(tipologia => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: tipologia,\n                children: tipologia\n              }, tipologia, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), activeTab === 1 && /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 2,\n        children: /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"Operatore\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: filters.operatore,\n            onChange: e => setFilters(prev => ({\n              ...prev,\n              operatore: e.target.value\n            })),\n            label: \"Operatore\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"\",\n              children: \"Tutti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 17\n            }, this), getUniqueValues(certificazioni, 'operatore').map(operatore => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: operatore,\n              children: operatore\n            }, operatore, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        sx: {\n          display: 'flex',\n          justifyContent: 'flex-end',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 24\n          }, this),\n          onClick: () => {\n            setSearchTerm('');\n            setFilters({\n              stato: '',\n              tipologia: '',\n              operatore: ''\n            });\n          },\n          children: \"Pulisci Filtri\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this), activeTab === 0 && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 26\n          }, this),\n          onClick: openCreateDialog,\n          children: \"Nuova Certificazione\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 326,\n    columnNumber: 5\n  }, this);\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    const currentItems = getCurrentPageItems(filteredCavi);\n    if (filteredCavi.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: searchTerm || filters.stato || filters.tipologia ? 'Nessun cavo trovato con i filtri applicati' : 'Nessun cavo disponibile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 449,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Tipologia\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Sezione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Metri\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Stato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Certificato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: currentItems.map(cavo => {\n              const isCertificato = certificazioni.some(cert => cert.id_cavo === cavo.id_cavo);\n              return /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    children: cavo.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.tipologia\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.sezione\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.ubicazione_partenza\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: cavo.ubicazione_arrivo\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: [cavo.metratura_reale || cavo.metri_teorici, \" m\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: cavo.stato_installazione,\n                    color: cavo.stato_installazione === 'INSTALLATO' ? 'success' : 'default'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: isCertificato ? /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    icon: /*#__PURE__*/_jsxDEV(CheckIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 486,\n                      columnNumber: 33\n                    }, this),\n                    label: \"Certificato\",\n                    color: \"success\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    icon: /*#__PURE__*/_jsxDEV(WarningIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 493,\n                      columnNumber: 33\n                    }, this),\n                    label: \"Non certificato\",\n                    color: \"warning\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 491,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Crea certificazione\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => {\n                        handleCavoSelect(cavo);\n                        openCreateDialog();\n                      },\n                      disabled: isCertificato,\n                      children: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 509,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 501,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 500,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 21\n                }, this)]\n              }, cavo.id_cavo, true, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 9\n      }, this), getTotalPages(filteredCavi) > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          count: getTotalPages(filteredCavi),\n          page: currentPage,\n          onChange: (event, value) => setCurrentPage(value),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true);\n  };\n\n  // Renderizza la tabella delle certificazioni\n  const renderCertificazioniTable = () => {\n    const currentItems = getCurrentPageItems(filteredCertificazioni);\n    if (filteredCertificazioni.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: searchTerm || filters.operatore ? 'Nessuna certificazione trovata con i filtri applicati' : 'Nessuna certificazione disponibile'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"N\\xB0 Certificato\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"ID Cavo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Data\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Operatore\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Strumento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Lunghezza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Isolamento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Azioni\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: currentItems.map(cert => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  fontWeight: \"medium\",\n                  children: cert.numero_certificato\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 568,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cert.id_cavo\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: new Date(cert.data_certificazione).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cert.operatore || cert.id_operatore\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: cert.strumento || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [cert.lunghezza_misurata, \" m\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  size: \"small\",\n                  label: `${cert.valore_isolamento} MΩ`,\n                  color: parseFloat(cert.valore_isolamento) >= 500 ? 'success' : 'warning'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 578,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Stack, {\n                  direction: \"row\",\n                  spacing: 0.5,\n                  children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Visualizza dettagli\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => {\n                        setSelectedItem(cert);\n                        setDialogType('view');\n                        setOpenDialog(true);\n                      },\n                      children: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 595,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 587,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 586,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Genera PDF\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleGeneratePdf(cert),\n                      disabled: loading,\n                      children: /*#__PURE__*/_jsxDEV(PdfIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 604,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Elimina\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      color: \"error\",\n                      onClick: () => handleDeleteCertificazione(cert),\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 613,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 608,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 607,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 19\n              }, this)]\n            }, cert.id_certificazione, true, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 550,\n        columnNumber: 9\n      }, this), getTotalPages(filteredCertificazioni) > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          count: getTotalPages(filteredCertificazioni),\n          page: currentPage,\n          onChange: (event, value) => setCurrentPage(value),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 626,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 625,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true);\n  };\n\n  // Renderizza il dialog per creare/modificare certificazione\n  const renderCertificazioneDialog = () => {\n    if (dialogType !== 'create' && dialogType !== 'edit') return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: closeDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: dialogType === 'create' ? 'Nuova Certificazione' : 'Modifica Certificazione'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 644,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Autocomplete, {\n              options: cavi.filter(cavo => !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo) || cavo.id_cavo === formData.id_cavo),\n              getOptionLabel: option => `${option.id_cavo} - ${option.tipologia}`,\n              value: cavi.find(c => c.id_cavo === formData.id_cavo) || null,\n              onChange: (event, newValue) => {\n                if (newValue) {\n                  handleCavoSelect(newValue);\n                } else {\n                  setFormData(prev => ({\n                    ...prev,\n                    id_cavo: '',\n                    lunghezza_misurata: ''\n                  }));\n                }\n              },\n              renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                ...params,\n                label: \"Cavo *\",\n                placeholder: \"Seleziona un cavo\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 665,\n                columnNumber: 19\n              }, this),\n              renderOption: (props, option) => /*#__PURE__*/_jsxDEV(Box, {\n                component: \"li\",\n                ...props,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    fontWeight: \"medium\",\n                    children: option.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 675,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: [option.tipologia, \" - \", option.ubicazione_partenza, \" \\u2192 \", option.ubicazione_arrivo]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 678,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 674,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 649,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Operatore *\",\n              value: formData.id_operatore,\n              onChange: e => handleFormChange('id_operatore', e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Strumento *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 699,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.id_strumento,\n                onChange: e => handleFormChange('id_strumento', e.target.value),\n                label: \"Strumento *\",\n                children: strumenti.map(strumento => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: strumento.id_strumento,\n                  children: [strumento.nome, \" - \", strumento.marca, \" \", strumento.modello]\n                }, strumento.id_strumento, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Lunghezza Misurata (m) *\",\n              type: \"number\",\n              value: formData.lunghezza_misurata,\n              onChange: e => handleFormChange('lunghezza_misurata', e.target.value),\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 715,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Continuit\\xE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.valore_continuita,\n                onChange: e => handleFormChange('valore_continuita', e.target.value),\n                label: \"Continuit\\xE0\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"OK\",\n                  children: \"OK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 733,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NOK\",\n                  children: \"NOK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 734,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 726,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 725,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Isolamento (M\\u03A9) *\",\n              type: \"number\",\n              value: formData.valore_isolamento,\n              onChange: e => handleFormChange('valore_isolamento', e.target.value),\n              required: true,\n              helperText: \"Valore minimo consigliato: 500 M\\u03A9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 740,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 739,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(FormControl, {\n              fullWidth: true,\n              children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                children: \"Resistenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                value: formData.valore_resistenza,\n                onChange: e => handleFormChange('valore_resistenza', e.target.value),\n                label: \"Resistenza\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"OK\",\n                  children: \"OK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 759,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"NOK\",\n                  children: \"NOK\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 760,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Note\",\n              multiline: true,\n              rows: 3,\n              value: formData.note,\n              onChange: e => handleFormChange('note', e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 766,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 765,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 647,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeDialog,\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 778,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCreateCertificazione,\n          variant: \"contained\",\n          disabled: loading || !formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento,\n          startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 783,\n            columnNumber: 34\n          }, this) : /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 783,\n            columnNumber: 67\n          }, this),\n          children: dialogType === 'create' ? 'Crea Certificazione' : 'Salva Modifiche'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 777,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 643,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza il dialog di visualizzazione dettagli\n  const renderViewDialog = () => {\n    if (dialogType !== 'view' || !selectedItem) return null;\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: closeDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [\"Dettagli Certificazione - \", selectedItem.numero_certificato]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 798,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Informazioni Cavo\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 806,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"ID Cavo: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.id_cavo\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 810,\n                    columnNumber: 30\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 809,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Lunghezza Misurata: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [selectedItem.lunghezza_misurata, \" m\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 813,\n                    columnNumber: 41\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 812,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 805,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 804,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 803,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Informazioni Certificazione\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 822,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Numero: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.numero_certificato\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 826,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 825,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Data: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: new Date(selectedItem.data_certificazione).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 829,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 828,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: [\"Operatore: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedItem.operatore || selectedItem.id_operatore\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 832,\n                    columnNumber: 32\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 831,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 821,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 820,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 819,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Risultati Test\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 841,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Continuit\\xE0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 846,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: selectedItem.valore_continuita,\n                      color: selectedItem.valore_continuita === 'OK' ? 'success' : 'error'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 849,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 845,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Isolamento\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 856,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: `${selectedItem.valore_isolamento} MΩ`,\n                      color: parseFloat(selectedItem.valore_isolamento) >= 500 ? 'success' : 'warning'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 859,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 855,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 4,\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"Resistenza\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 866,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                      size: \"small\",\n                      label: selectedItem.valore_resistenza,\n                      color: selectedItem.valore_resistenza === 'OK' ? 'success' : 'error'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 869,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 865,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 844,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 13\n          }, this), selectedItem.note && /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Note\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 884,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: selectedItem.note\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 887,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 883,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 882,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 881,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 802,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 801,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeDialog,\n          children: \"Chiudi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 897,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => handleGeneratePdf(selectedItem),\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(PdfIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 901,\n            columnNumber: 24\n          }, this),\n          disabled: loading,\n          children: \"Genera PDF\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 898,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 896,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 797,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Renderizza le statistiche\n  const renderStats = () => {\n    const totalCavi = cavi.length;\n    const caviInstallati = cavi.filter(c => c.stato_installazione === 'INSTALLATO').length;\n    const caviCertificati = certificazioni.length;\n    const percentualeCertificazione = totalCavi > 0 ? Math.round(caviCertificati / caviInstallati * 100) : 0;\n    return /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 2,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Cavi Totali\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 923,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              children: totalCavi\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 926,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 922,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 921,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 920,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Cavi Installati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 935,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              children: caviInstallati\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 938,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 934,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 933,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 932,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Certificazioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 947,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              children: caviCertificati\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 950,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 946,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 945,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 944,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        sm: 6,\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"% Certificazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 959,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: percentualeCertificazione >= 80 ? 'success.main' : 'warning.main',\n              children: [percentualeCertificazione, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 962,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 958,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 957,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 956,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 919,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [renderStats(), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: activeTab,\n        onChange: handleTabChange,\n        indicatorColor: \"primary\",\n        textColor: \"primary\",\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          label: `Cavi (${filteredCavi.length})`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 983,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          label: `Certificazioni (${filteredCertificazioni.length})`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 984,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 977,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 976,\n      columnNumber: 7\n    }, this), renderSearchAndFilters(), loading && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 992,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 991,\n      columnNumber: 9\n    }, this), !loading && activeTab === 0 && renderCaviTable(), !loading && activeTab === 1 && renderCertificazioniTable(), renderCertificazioneDialog(), renderViewDialog()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 973,\n    columnNumber: 5\n  }, this);\n}, \"+5XhGVN2W0F1tnaaerfg1TNwMBg=\")), \"+5XhGVN2W0F1tnaaerfg1TNwMBg=\");\n_c2 = CertificazioneCaviImproved;\nexport default CertificazioneCaviImproved;\nvar _c, _c2;\n$RefreshReg$(_c, \"CertificazioneCaviImproved$forwardRef\");\n$RefreshReg$(_c2, \"CertificazioneCaviImproved\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useImperativeHandle", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Autocomplete", "CircularProgress", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "IconButton", "Tabs", "Tab", "Pagination", "InputAdornment", "Divider", "<PERSON><PERSON>", "Chip", "<PERSON><PERSON><PERSON>", "Badge", "Add", "AddIcon", "Search", "SearchIcon", "FilterList", "FilterIcon", "PictureAsPdf", "PdfIcon", "Download", "DownloadIcon", "Visibility", "ViewIcon", "Delete", "DeleteIcon", "Edit", "EditIcon", "Save", "SaveIcon", "Clear", "ClearIcon", "Build", "BuildIcon", "CheckCircle", "CheckIcon", "Warning", "WarningIcon", "certificazioneService", "caviService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CertificazioneCaviImproved", "_s", "_c", "cantiereId", "onSuccess", "onError", "ref", "loading", "setLoading", "activeTab", "setActiveTab", "certificazioni", "setCertificazioni", "cavi", "<PERSON><PERSON><PERSON>", "strumenti", "setStrumenti", "searchTerm", "setSearchTerm", "filteredCavi", "setFilteredCavi", "filteredCertificazioni", "setFilteredCertificazioni", "filters", "setFilters", "stato", "tipologia", "operatore", "currentPage", "setCurrentPage", "itemsPerPage", "openDialog", "setOpenDialog", "dialogType", "setDialogType", "selectedItem", "setSelectedItem", "formData", "setFormData", "id_cavo", "id_operatore", "id_strumento", "<PERSON><PERSON><PERSON>_misurata", "valore_continuita", "valore_isolamento", "valore_resistenza", "note", "loadInitialData", "filterCavi", "filterCertificazioni", "Promise", "all", "loadCertificazioni", "loadCavi", "loadStrumenti", "error", "data", "getCertificazioni", "console", "get<PERSON><PERSON>", "getStrumenti", "filtered", "filter", "cavo", "_cavo$tipologia", "_cavo$ubicazione_part", "_cavo$ubicazione_arri", "toLowerCase", "includes", "ubicazione_partenza", "ubicazione_arrivo", "stato_installazione", "cert", "_cert$operatore", "_cert$numero_certific", "numero_certificato", "handleTabChange", "event", "newValue", "openCreateDialog", "closeDialog", "handleFormChange", "field", "value", "prev", "handleCavoSelect", "metratura_reale", "metri_te<PERSON>ci", "handleCreateCertificazione", "createCertificazione", "message", "handleGeneratePdf", "certificazione", "response", "generatePdf", "id_certificazione", "file_url", "window", "open", "handleDeleteCertificazione", "confirm", "deleteCertificazione", "handleOptionSelect", "option", "getCurrentPageItems", "items", "startIndex", "endIndex", "slice", "getTotalPages", "Math", "ceil", "length", "getUniqueValues", "array", "Set", "map", "item", "Boolean", "renderSearchAndFilters", "sx", "p", "mb", "children", "container", "spacing", "alignItems", "xs", "md", "fullWidth", "placeholder", "onChange", "e", "target", "InputProps", "startAdornment", "position", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "endAdornment", "size", "onClick", "label", "display", "justifyContent", "gap", "variant", "startIcon", "renderCaviTable", "currentItems", "severity", "component", "isCertificato", "some", "fontWeight", "sezione", "color", "icon", "title", "disabled", "mt", "count", "page", "renderCertificazioniTable", "Date", "data_certificazione", "toLocaleDateString", "strumento", "parseFloat", "direction", "renderCertificazioneDialog", "onClose", "max<PERSON><PERSON><PERSON>", "options", "getOptionLabel", "find", "c", "renderInput", "params", "required", "renderOption", "props", "nome", "marca", "modello", "type", "helperText", "multiline", "rows", "renderViewDialog", "gutterBottom", "renderStats", "totalCavi", "caviInstallati", "caviCertificati", "percentualeCertificazione", "round", "sm", "indicatorColor", "textColor", "my", "_c2", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/cavi/CertificazioneCaviImproved.js"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Autocomplete,\n  CircularProgress,\n  Alert,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  IconButton,\n  Tabs,\n  Tab,\n  Pagination,\n  InputAdornment,\n  Divider,\n  Stack,\n  Chip,\n  Tooltip,\n  Badge\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Search as SearchIcon,\n  FilterList as FilterIcon,\n  PictureAsPdf as PdfIcon,\n  Download as DownloadIcon,\n  Visibility as ViewIcon,\n  Delete as DeleteIcon,\n  Edit as EditIcon,\n  Save as SaveIcon,\n  Clear as ClearIcon,\n  Build as BuildIcon,\n  CheckCircle as CheckIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\n\nimport certificazioneService from '../../services/certificazioneService';\nimport caviService from '../../services/caviService';\n\nconst CertificazioneCaviImproved = forwardRef(({ cantiereId, onSuccess, onError }, ref) => {\n  // Stati principali\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState(0);\n  const [certificazioni, setCertificazioni] = useState([]);\n  const [cavi, setCavi] = useState([]);\n  const [strumenti, setStrumenti] = useState([]);\n  \n  // Stati per ricerca e filtri\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filteredCavi, setFilteredCavi] = useState([]);\n  const [filteredCertificazioni, setFilteredCertificazioni] = useState([]);\n  const [filters, setFilters] = useState({\n    stato: '',\n    tipologia: '',\n    operatore: ''\n  });\n  \n  // Stati per paginazione\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10);\n  \n  // Stati per dialogs\n  const [openDialog, setOpenDialog] = useState(false);\n  const [dialogType, setDialogType] = useState('');\n  const [selectedItem, setSelectedItem] = useState(null);\n  \n  // Stati per form certificazione\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    id_operatore: '',\n    id_strumento: '',\n    lunghezza_misurata: '',\n    valore_continuita: 'OK',\n    valore_isolamento: '',\n    valore_resistenza: 'OK',\n    note: ''\n  });\n\n  // Carica dati iniziali\n  useEffect(() => {\n    loadInitialData();\n  }, [cantiereId]);\n\n  // Filtra cavi in base alla ricerca\n  useEffect(() => {\n    filterCavi();\n  }, [cavi, searchTerm, filters]);\n\n  // Filtra certificazioni\n  useEffect(() => {\n    filterCertificazioni();\n  }, [certificazioni, searchTerm]);\n\n  const loadInitialData = async () => {\n    try {\n      setLoading(true);\n      await Promise.all([\n        loadCertificazioni(),\n        loadCavi(),\n        loadStrumenti()\n      ]);\n    } catch (error) {\n      onError('Errore nel caricamento dei dati iniziali');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadCertificazioni = async () => {\n    try {\n      const data = await certificazioneService.getCertificazioni(cantiereId);\n      setCertificazioni(data);\n    } catch (error) {\n      console.error('Errore nel caricamento delle certificazioni:', error);\n      throw error;\n    }\n  };\n\n  const loadCavi = async () => {\n    try {\n      const data = await caviService.getCavi(cantiereId);\n      setCavi(data);\n    } catch (error) {\n      console.error('Errore nel caricamento dei cavi:', error);\n      throw error;\n    }\n  };\n\n  const loadStrumenti = async () => {\n    try {\n      const data = await certificazioneService.getStrumenti(cantiereId);\n      setStrumenti(data);\n    } catch (error) {\n      console.error('Errore nel caricamento degli strumenti:', error);\n      throw error;\n    }\n  };\n\n  const filterCavi = () => {\n    let filtered = cavi;\n\n    // Filtro per ricerca testuale\n    if (searchTerm) {\n      filtered = filtered.filter(cavo =>\n        cavo.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        cavo.tipologia?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        cavo.ubicazione_partenza?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        cavo.ubicazione_arrivo?.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    }\n\n    // Filtri specifici\n    if (filters.stato) {\n      filtered = filtered.filter(cavo => cavo.stato_installazione === filters.stato);\n    }\n    if (filters.tipologia) {\n      filtered = filtered.filter(cavo => cavo.tipologia === filters.tipologia);\n    }\n\n    setFilteredCavi(filtered);\n  };\n\n  const filterCertificazioni = () => {\n    let filtered = certificazioni;\n\n    if (searchTerm) {\n      filtered = filtered.filter(cert =>\n        cert.id_cavo.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        cert.operatore?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        cert.numero_certificato?.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n    }\n\n    if (filters.operatore) {\n      filtered = filtered.filter(cert => cert.operatore === filters.operatore);\n    }\n\n    setFilteredCertificazioni(filtered);\n  };\n\n  // Gestione tabs\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setCurrentPage(1);\n    setSearchTerm('');\n    setFilters({ stato: '', tipologia: '', operatore: '' });\n  };\n\n  // Gestione dialogs\n  const openCreateDialog = () => {\n    setDialogType('create');\n    setSelectedItem(null);\n    setFormData({\n      id_cavo: '',\n      id_operatore: '',\n      id_strumento: '',\n      lunghezza_misurata: '',\n      valore_continuita: 'OK',\n      valore_isolamento: '',\n      valore_resistenza: 'OK',\n      note: ''\n    });\n    setOpenDialog(true);\n  };\n\n  const closeDialog = () => {\n    setOpenDialog(false);\n    setSelectedItem(null);\n    setDialogType('');\n  };\n\n  // Gestione form\n  const handleFormChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleCavoSelect = (cavo) => {\n    setFormData(prev => ({\n      ...prev,\n      id_cavo: cavo.id_cavo,\n      lunghezza_misurata: cavo.metratura_reale || cavo.metri_teorici || ''\n    }));\n  };\n\n  // Operazioni CRUD\n  const handleCreateCertificazione = async () => {\n    try {\n      if (!formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento) {\n        onError('Compila tutti i campi obbligatori');\n        return;\n      }\n\n      setLoading(true);\n      await certificazioneService.createCertificazione(cantiereId, formData);\n      onSuccess('Certificazione creata con successo');\n      closeDialog();\n      await loadCertificazioni();\n    } catch (error) {\n      onError('Errore nella creazione della certificazione: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleGeneratePdf = async (certificazione) => {\n    try {\n      setLoading(true);\n      const response = await certificazioneService.generatePdf(cantiereId, certificazione.id_certificazione);\n      \n      if (response.file_url) {\n        window.open(response.file_url, '_blank');\n        onSuccess('PDF generato con successo');\n      } else {\n        onError('Errore nella generazione del PDF');\n      }\n    } catch (error) {\n      onError('Errore nella generazione del PDF: ' + (error.message || 'Errore sconosciuto'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteCertificazione = async (certificazione) => {\n    if (window.confirm(`Sei sicuro di voler eliminare la certificazione ${certificazione.numero_certificato}?`)) {\n      try {\n        setLoading(true);\n        await certificazioneService.deleteCertificazione(cantiereId, certificazione.id_certificazione);\n        onSuccess('Certificazione eliminata con successo');\n        await loadCertificazioni();\n      } catch (error) {\n        onError('Errore nell\\'eliminazione della certificazione: ' + (error.message || 'Errore sconosciuto'));\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n\n  // Espone metodi tramite ref\n  useImperativeHandle(ref, () => ({\n    handleOptionSelect: (option) => {\n      if (option === 'creaCertificazione') {\n        openCreateDialog();\n      } else if (option === 'visualizzaCertificazioni') {\n        setActiveTab(1);\n      }\n    }\n  }));\n\n  // Calcola elementi per paginazione\n  const getCurrentPageItems = (items) => {\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const endIndex = startIndex + itemsPerPage;\n    return items.slice(startIndex, endIndex);\n  };\n\n  const getTotalPages = (items) => Math.ceil(items.length / itemsPerPage);\n\n  // Ottieni opzioni uniche per filtri\n  const getUniqueValues = (array, field) => {\n    return [...new Set(array.map(item => item[field]).filter(Boolean))];\n  };\n\n  // Renderizza la barra di ricerca e filtri\n  const renderSearchAndFilters = () => (\n    <Paper sx={{ p: 2, mb: 2 }}>\n      <Grid container spacing={2} alignItems=\"center\">\n        <Grid item xs={12} md={4}>\n          <TextField\n            fullWidth\n            placeholder=\"Cerca per ID cavo, tipologia, ubicazione...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon />\n                </InputAdornment>\n              ),\n              endAdornment: searchTerm && (\n                <InputAdornment position=\"end\">\n                  <IconButton size=\"small\" onClick={() => setSearchTerm('')}>\n                    <ClearIcon />\n                  </IconButton>\n                </InputAdornment>\n              )\n            }}\n          />\n        </Grid>\n\n        {activeTab === 0 && (\n          <>\n            <Grid item xs={12} md={2}>\n              <FormControl fullWidth size=\"small\">\n                <InputLabel>Stato</InputLabel>\n                <Select\n                  value={filters.stato}\n                  onChange={(e) => setFilters(prev => ({ ...prev, stato: e.target.value }))}\n                  label=\"Stato\"\n                >\n                  <MenuItem value=\"\">Tutti</MenuItem>\n                  {getUniqueValues(cavi, 'stato_installazione').map(stato => (\n                    <MenuItem key={stato} value={stato}>{stato}</MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n            <Grid item xs={12} md={2}>\n              <FormControl fullWidth size=\"small\">\n                <InputLabel>Tipologia</InputLabel>\n                <Select\n                  value={filters.tipologia}\n                  onChange={(e) => setFilters(prev => ({ ...prev, tipologia: e.target.value }))}\n                  label=\"Tipologia\"\n                >\n                  <MenuItem value=\"\">Tutte</MenuItem>\n                  {getUniqueValues(cavi, 'tipologia').map(tipologia => (\n                    <MenuItem key={tipologia} value={tipologia}>{tipologia}</MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n          </>\n        )}\n\n        {activeTab === 1 && (\n          <Grid item xs={12} md={2}>\n            <FormControl fullWidth size=\"small\">\n              <InputLabel>Operatore</InputLabel>\n              <Select\n                value={filters.operatore}\n                onChange={(e) => setFilters(prev => ({ ...prev, operatore: e.target.value }))}\n                label=\"Operatore\"\n              >\n                <MenuItem value=\"\">Tutti</MenuItem>\n                {getUniqueValues(certificazioni, 'operatore').map(operatore => (\n                  <MenuItem key={operatore} value={operatore}>{operatore}</MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Grid>\n        )}\n\n        <Grid item xs={12} md={4} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>\n          <Button\n            variant=\"outlined\"\n            startIcon={<ClearIcon />}\n            onClick={() => {\n              setSearchTerm('');\n              setFilters({ stato: '', tipologia: '', operatore: '' });\n            }}\n          >\n            Pulisci Filtri\n          </Button>\n          {activeTab === 0 && (\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={openCreateDialog}\n            >\n              Nuova Certificazione\n            </Button>\n          )}\n        </Grid>\n      </Grid>\n    </Paper>\n  );\n\n  // Renderizza la tabella dei cavi\n  const renderCaviTable = () => {\n    const currentItems = getCurrentPageItems(filteredCavi);\n\n    if (filteredCavi.length === 0) {\n      return (\n        <Alert severity=\"info\">\n          {searchTerm || filters.stato || filters.tipologia\n            ? 'Nessun cavo trovato con i filtri applicati'\n            : 'Nessun cavo disponibile'}\n        </Alert>\n      );\n    }\n\n    return (\n      <>\n        <TableContainer component={Paper}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>Tipologia</TableCell>\n                <TableCell>Sezione</TableCell>\n                <TableCell>Partenza</TableCell>\n                <TableCell>Arrivo</TableCell>\n                <TableCell>Metri</TableCell>\n                <TableCell>Stato</TableCell>\n                <TableCell>Certificato</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {currentItems.map((cavo) => {\n                const isCertificato = certificazioni.some(cert => cert.id_cavo === cavo.id_cavo);\n                return (\n                  <TableRow key={cavo.id_cavo}>\n                    <TableCell>\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        {cavo.id_cavo}\n                      </Typography>\n                    </TableCell>\n                    <TableCell>{cavo.tipologia}</TableCell>\n                    <TableCell>{cavo.sezione}</TableCell>\n                    <TableCell>{cavo.ubicazione_partenza}</TableCell>\n                    <TableCell>{cavo.ubicazione_arrivo}</TableCell>\n                    <TableCell>{cavo.metratura_reale || cavo.metri_teorici} m</TableCell>\n                    <TableCell>\n                      <Chip\n                        size=\"small\"\n                        label={cavo.stato_installazione}\n                        color={cavo.stato_installazione === 'INSTALLATO' ? 'success' : 'default'}\n                      />\n                    </TableCell>\n                    <TableCell>\n                      {isCertificato ? (\n                        <Chip\n                          size=\"small\"\n                          icon={<CheckIcon />}\n                          label=\"Certificato\"\n                          color=\"success\"\n                        />\n                      ) : (\n                        <Chip\n                          size=\"small\"\n                          icon={<WarningIcon />}\n                          label=\"Non certificato\"\n                          color=\"warning\"\n                        />\n                      )}\n                    </TableCell>\n                    <TableCell>\n                      <Tooltip title=\"Crea certificazione\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => {\n                            handleCavoSelect(cavo);\n                            openCreateDialog();\n                          }}\n                          disabled={isCertificato}\n                        >\n                          <AddIcon />\n                        </IconButton>\n                      </Tooltip>\n                    </TableCell>\n                  </TableRow>\n                );\n              })}\n            </TableBody>\n          </Table>\n        </TableContainer>\n\n        {getTotalPages(filteredCavi) > 1 && (\n          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n            <Pagination\n              count={getTotalPages(filteredCavi)}\n              page={currentPage}\n              onChange={(event, value) => setCurrentPage(value)}\n              color=\"primary\"\n            />\n          </Box>\n        )}\n      </>\n    );\n  };\n\n  // Renderizza la tabella delle certificazioni\n  const renderCertificazioniTable = () => {\n    const currentItems = getCurrentPageItems(filteredCertificazioni);\n\n    if (filteredCertificazioni.length === 0) {\n      return (\n        <Alert severity=\"info\">\n          {searchTerm || filters.operatore\n            ? 'Nessuna certificazione trovata con i filtri applicati'\n            : 'Nessuna certificazione disponibile'}\n        </Alert>\n      );\n    }\n\n    return (\n      <>\n        <TableContainer component={Paper}>\n          <Table size=\"small\">\n            <TableHead>\n              <TableRow>\n                <TableCell>N° Certificato</TableCell>\n                <TableCell>ID Cavo</TableCell>\n                <TableCell>Data</TableCell>\n                <TableCell>Operatore</TableCell>\n                <TableCell>Strumento</TableCell>\n                <TableCell>Lunghezza</TableCell>\n                <TableCell>Isolamento</TableCell>\n                <TableCell>Azioni</TableCell>\n              </TableRow>\n            </TableHead>\n            <TableBody>\n              {currentItems.map((cert) => (\n                <TableRow key={cert.id_certificazione}>\n                  <TableCell>\n                    <Typography variant=\"body2\" fontWeight=\"medium\">\n                      {cert.numero_certificato}\n                    </Typography>\n                  </TableCell>\n                  <TableCell>{cert.id_cavo}</TableCell>\n                  <TableCell>{new Date(cert.data_certificazione).toLocaleDateString()}</TableCell>\n                  <TableCell>{cert.operatore || cert.id_operatore}</TableCell>\n                  <TableCell>{cert.strumento || 'N/A'}</TableCell>\n                  <TableCell>{cert.lunghezza_misurata} m</TableCell>\n                  <TableCell>\n                    <Chip\n                      size=\"small\"\n                      label={`${cert.valore_isolamento} MΩ`}\n                      color={parseFloat(cert.valore_isolamento) >= 500 ? 'success' : 'warning'}\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <Stack direction=\"row\" spacing={0.5}>\n                      <Tooltip title=\"Visualizza dettagli\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => {\n                            setSelectedItem(cert);\n                            setDialogType('view');\n                            setOpenDialog(true);\n                          }}\n                        >\n                          <ViewIcon />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Genera PDF\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleGeneratePdf(cert)}\n                          disabled={loading}\n                        >\n                          <PdfIcon />\n                        </IconButton>\n                      </Tooltip>\n                      <Tooltip title=\"Elimina\">\n                        <IconButton\n                          size=\"small\"\n                          color=\"error\"\n                          onClick={() => handleDeleteCertificazione(cert)}\n                        >\n                          <DeleteIcon />\n                        </IconButton>\n                      </Tooltip>\n                    </Stack>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </TableContainer>\n\n        {getTotalPages(filteredCertificazioni) > 1 && (\n          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\n            <Pagination\n              count={getTotalPages(filteredCertificazioni)}\n              page={currentPage}\n              onChange={(event, value) => setCurrentPage(value)}\n              color=\"primary\"\n            />\n          </Box>\n        )}\n      </>\n    );\n  };\n\n  // Renderizza il dialog per creare/modificare certificazione\n  const renderCertificazioneDialog = () => {\n    if (dialogType !== 'create' && dialogType !== 'edit') return null;\n\n    return (\n      <Dialog open={openDialog} onClose={closeDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          {dialogType === 'create' ? 'Nuova Certificazione' : 'Modifica Certificazione'}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <Autocomplete\n                options={cavi.filter(cavo =>\n                  !certificazioni.some(cert => cert.id_cavo === cavo.id_cavo) ||\n                  cavo.id_cavo === formData.id_cavo\n                )}\n                getOptionLabel={(option) => `${option.id_cavo} - ${option.tipologia}`}\n                value={cavi.find(c => c.id_cavo === formData.id_cavo) || null}\n                onChange={(event, newValue) => {\n                  if (newValue) {\n                    handleCavoSelect(newValue);\n                  } else {\n                    setFormData(prev => ({ ...prev, id_cavo: '', lunghezza_misurata: '' }));\n                  }\n                }}\n                renderInput={(params) => (\n                  <TextField\n                    {...params}\n                    label=\"Cavo *\"\n                    placeholder=\"Seleziona un cavo\"\n                    required\n                  />\n                )}\n                renderOption={(props, option) => (\n                  <Box component=\"li\" {...props}>\n                    <Box>\n                      <Typography variant=\"body2\" fontWeight=\"medium\">\n                        {option.id_cavo}\n                      </Typography>\n                      <Typography variant=\"caption\" color=\"text.secondary\">\n                        {option.tipologia} - {option.ubicazione_partenza} → {option.ubicazione_arrivo}\n                      </Typography>\n                    </Box>\n                  </Box>\n                )}\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Operatore *\"\n                value={formData.id_operatore}\n                onChange={(e) => handleFormChange('id_operatore', e.target.value)}\n                required\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <FormControl fullWidth required>\n                <InputLabel>Strumento *</InputLabel>\n                <Select\n                  value={formData.id_strumento}\n                  onChange={(e) => handleFormChange('id_strumento', e.target.value)}\n                  label=\"Strumento *\"\n                >\n                  {strumenti.map((strumento) => (\n                    <MenuItem key={strumento.id_strumento} value={strumento.id_strumento}>\n                      {strumento.nome} - {strumento.marca} {strumento.modello}\n                    </MenuItem>\n                  ))}\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <TextField\n                fullWidth\n                label=\"Lunghezza Misurata (m) *\"\n                type=\"number\"\n                value={formData.lunghezza_misurata}\n                onChange={(e) => handleFormChange('lunghezza_misurata', e.target.value)}\n                required\n              />\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <FormControl fullWidth>\n                <InputLabel>Continuità</InputLabel>\n                <Select\n                  value={formData.valore_continuita}\n                  onChange={(e) => handleFormChange('valore_continuita', e.target.value)}\n                  label=\"Continuità\"\n                >\n                  <MenuItem value=\"OK\">OK</MenuItem>\n                  <MenuItem value=\"NOK\">NOK</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <TextField\n                fullWidth\n                label=\"Isolamento (MΩ) *\"\n                type=\"number\"\n                value={formData.valore_isolamento}\n                onChange={(e) => handleFormChange('valore_isolamento', e.target.value)}\n                required\n                helperText=\"Valore minimo consigliato: 500 MΩ\"\n              />\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <FormControl fullWidth>\n                <InputLabel>Resistenza</InputLabel>\n                <Select\n                  value={formData.valore_resistenza}\n                  onChange={(e) => handleFormChange('valore_resistenza', e.target.value)}\n                  label=\"Resistenza\"\n                >\n                  <MenuItem value=\"OK\">OK</MenuItem>\n                  <MenuItem value=\"NOK\">NOK</MenuItem>\n                </Select>\n              </FormControl>\n            </Grid>\n\n            <Grid item xs={12}>\n              <TextField\n                fullWidth\n                label=\"Note\"\n                multiline\n                rows={3}\n                value={formData.note}\n                onChange={(e) => handleFormChange('note', e.target.value)}\n              />\n            </Grid>\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeDialog}>Annulla</Button>\n          <Button\n            onClick={handleCreateCertificazione}\n            variant=\"contained\"\n            disabled={loading || !formData.id_cavo || !formData.id_operatore || !formData.id_strumento || !formData.valore_isolamento}\n            startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}\n          >\n            {dialogType === 'create' ? 'Crea Certificazione' : 'Salva Modifiche'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Renderizza il dialog di visualizzazione dettagli\n  const renderViewDialog = () => {\n    if (dialogType !== 'view' || !selectedItem) return null;\n\n    return (\n      <Dialog open={openDialog} onClose={closeDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          Dettagli Certificazione - {selectedItem.numero_certificato}\n        </DialogTitle>\n        <DialogContent>\n          <Grid container spacing={2} sx={{ mt: 1 }}>\n            <Grid item xs={12} md={6}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Informazioni Cavo\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    ID Cavo: <strong>{selectedItem.id_cavo}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Lunghezza Misurata: <strong>{selectedItem.lunghezza_misurata} m</strong>\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Informazioni Certificazione\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Numero: <strong>{selectedItem.numero_certificato}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Data: <strong>{new Date(selectedItem.data_certificazione).toLocaleDateString()}</strong>\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    Operatore: <strong>{selectedItem.operatore || selectedItem.id_operatore}</strong>\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            <Grid item xs={12}>\n              <Card variant=\"outlined\">\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    Risultati Test\n                  </Typography>\n                  <Grid container spacing={2}>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Continuità\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={selectedItem.valore_continuita}\n                        color={selectedItem.valore_continuita === 'OK' ? 'success' : 'error'}\n                      />\n                    </Grid>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Isolamento\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={`${selectedItem.valore_isolamento} MΩ`}\n                        color={parseFloat(selectedItem.valore_isolamento) >= 500 ? 'success' : 'warning'}\n                      />\n                    </Grid>\n                    <Grid item xs={4}>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        Resistenza\n                      </Typography>\n                      <Chip\n                        size=\"small\"\n                        label={selectedItem.valore_resistenza}\n                        color={selectedItem.valore_resistenza === 'OK' ? 'success' : 'error'}\n                      />\n                    </Grid>\n                  </Grid>\n                </CardContent>\n              </Card>\n            </Grid>\n\n            {selectedItem.note && (\n              <Grid item xs={12}>\n                <Card variant=\"outlined\">\n                  <CardContent>\n                    <Typography variant=\"h6\" gutterBottom>\n                      Note\n                    </Typography>\n                    <Typography variant=\"body2\">\n                      {selectedItem.note}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              </Grid>\n            )}\n          </Grid>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={closeDialog}>Chiudi</Button>\n          <Button\n            onClick={() => handleGeneratePdf(selectedItem)}\n            variant=\"contained\"\n            startIcon={<PdfIcon />}\n            disabled={loading}\n          >\n            Genera PDF\n          </Button>\n        </DialogActions>\n      </Dialog>\n    );\n  };\n\n  // Renderizza le statistiche\n  const renderStats = () => {\n    const totalCavi = cavi.length;\n    const caviInstallati = cavi.filter(c => c.stato_installazione === 'INSTALLATO').length;\n    const caviCertificati = certificazioni.length;\n    const percentualeCertificazione = totalCavi > 0 ? Math.round((caviCertificati / caviInstallati) * 100) : 0;\n\n    return (\n      <Grid container spacing={2} sx={{ mb: 3 }}>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                Cavi Totali\n              </Typography>\n              <Typography variant=\"h4\">\n                {totalCavi}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                Cavi Installati\n              </Typography>\n              <Typography variant=\"h4\">\n                {caviInstallati}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                Certificazioni\n              </Typography>\n              <Typography variant=\"h4\">\n                {caviCertificati}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n        <Grid item xs={12} sm={6} md={3}>\n          <Card>\n            <CardContent>\n              <Typography color=\"text.secondary\" gutterBottom>\n                % Certificazione\n              </Typography>\n              <Typography variant=\"h4\" color={percentualeCertificazione >= 80 ? 'success.main' : 'warning.main'}>\n                {percentualeCertificazione}%\n              </Typography>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n    );\n  };\n\n  return (\n    <Box>\n      {renderStats()}\n\n      <Paper sx={{ mb: 2 }}>\n        <Tabs\n          value={activeTab}\n          onChange={handleTabChange}\n          indicatorColor=\"primary\"\n          textColor=\"primary\"\n        >\n          <Tab label={`Cavi (${filteredCavi.length})`} />\n          <Tab label={`Certificazioni (${filteredCertificazioni.length})`} />\n        </Tabs>\n      </Paper>\n\n      {renderSearchAndFilters()}\n\n      {loading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {!loading && activeTab === 0 && renderCaviTable()}\n      {!loading && activeTab === 1 && renderCertificazioniTable()}\n\n      {renderCertificazioneDialog()}\n      {renderViewDialog()}\n    </Box>\n  );\n});\n\nexport default CertificazioneCaviImproved;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AACnF,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,YAAY,EACZC,gBAAgB,EAChBC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,cAAc,EACdC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,KAAK,QACA,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,UAAU,EACxBC,YAAY,IAAIC,OAAO,EACvBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,QAAQ,EACtBC,MAAM,IAAIC,UAAU,EACpBC,IAAI,IAAIC,QAAQ,EAChBC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,WAAW,IAAIC,SAAS,EACxBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAE5B,OAAOC,qBAAqB,MAAM,sCAAsC;AACxE,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,0BAA0B,gBAAAC,EAAA,cAAGtE,UAAU,CAAAuE,EAAA,GAAAD,EAAA,CAAC,CAAC;EAAEE,UAAU;EAAEC,SAAS;EAAEC;AAAQ,CAAC,EAAEC,GAAG,KAAK;EAAAL,EAAA;EACzF;EACA,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgF,SAAS,EAAEC,YAAY,CAAC,GAAGjF,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACkF,cAAc,EAAEC,iBAAiB,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACoF,IAAI,EAAEC,OAAO,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACsF,SAAS,EAAEC,YAAY,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM,CAACwF,UAAU,EAAEC,aAAa,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0F,YAAY,EAAEC,eAAe,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC4F,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EACxE,MAAM,CAAC8F,OAAO,EAAEC,UAAU,CAAC,GAAG/F,QAAQ,CAAC;IACrCgG,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpG,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACqG,YAAY,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;;EAEnC;EACA,MAAM,CAACsG,UAAU,EAAEC,aAAa,CAAC,GAAGvG,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwG,UAAU,EAAEC,aAAa,CAAC,GAAGzG,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0G,YAAY,EAAEC,eAAe,CAAC,GAAG3G,QAAQ,CAAC,IAAI,CAAC;;EAEtD;EACA,MAAM,CAAC4G,QAAQ,EAAEC,WAAW,CAAC,GAAG7G,QAAQ,CAAC;IACvC8G,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBC,kBAAkB,EAAE,EAAE;IACtBC,iBAAiB,EAAE,IAAI;IACvBC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE,IAAI;IACvBC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACApH,SAAS,CAAC,MAAM;IACdqH,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAAC5C,UAAU,CAAC,CAAC;;EAEhB;EACAzE,SAAS,CAAC,MAAM;IACdsH,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACnC,IAAI,EAAEI,UAAU,EAAEM,OAAO,CAAC,CAAC;;EAE/B;EACA7F,SAAS,CAAC,MAAM;IACduH,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAACtC,cAAc,EAAEM,UAAU,CAAC,CAAC;EAEhC,MAAM8B,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFvC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM0C,OAAO,CAACC,GAAG,CAAC,CAChBC,kBAAkB,CAAC,CAAC,EACpBC,QAAQ,CAAC,CAAC,EACVC,aAAa,CAAC,CAAC,CAChB,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdlD,OAAO,CAAC,0CAA0C,CAAC;IACrD,CAAC,SAAS;MACRG,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4C,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAMI,IAAI,GAAG,MAAM9D,qBAAqB,CAAC+D,iBAAiB,CAACtD,UAAU,CAAC;MACtES,iBAAiB,CAAC4C,IAAI,CAAC;IACzB,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACpE,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMF,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACF,MAAMG,IAAI,GAAG,MAAM7D,WAAW,CAACgE,OAAO,CAACxD,UAAU,CAAC;MAClDW,OAAO,CAAC0C,IAAI,CAAC;IACf,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAME,IAAI,GAAG,MAAM9D,qBAAqB,CAACkE,YAAY,CAACzD,UAAU,CAAC;MACjEa,YAAY,CAACwC,IAAI,CAAC;IACpB,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMP,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIa,QAAQ,GAAGhD,IAAI;;IAEnB;IACA,IAAII,UAAU,EAAE;MACd4C,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,IAAI;QAAA,IAAAC,eAAA,EAAAC,qBAAA,EAAAC,qBAAA;QAAA,OAC7BH,IAAI,CAACxB,OAAO,CAAC4B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnD,UAAU,CAACkD,WAAW,CAAC,CAAC,CAAC,MAAAH,eAAA,GAC7DD,IAAI,CAACrC,SAAS,cAAAsC,eAAA,uBAAdA,eAAA,CAAgBG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnD,UAAU,CAACkD,WAAW,CAAC,CAAC,CAAC,OAAAF,qBAAA,GAChEF,IAAI,CAACM,mBAAmB,cAAAJ,qBAAA,uBAAxBA,qBAAA,CAA0BE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnD,UAAU,CAACkD,WAAW,CAAC,CAAC,CAAC,OAAAD,qBAAA,GAC1EH,IAAI,CAACO,iBAAiB,cAAAJ,qBAAA,uBAAtBA,qBAAA,CAAwBC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnD,UAAU,CAACkD,WAAW,CAAC,CAAC,CAAC;MAAA,CAC1E,CAAC;IACH;;IAEA;IACA,IAAI5C,OAAO,CAACE,KAAK,EAAE;MACjBoC,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACQ,mBAAmB,KAAKhD,OAAO,CAACE,KAAK,CAAC;IAChF;IACA,IAAIF,OAAO,CAACG,SAAS,EAAE;MACrBmC,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACrC,SAAS,KAAKH,OAAO,CAACG,SAAS,CAAC;IAC1E;IAEAN,eAAe,CAACyC,QAAQ,CAAC;EAC3B,CAAC;EAED,MAAMZ,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIY,QAAQ,GAAGlD,cAAc;IAE7B,IAAIM,UAAU,EAAE;MACd4C,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACU,IAAI;QAAA,IAAAC,eAAA,EAAAC,qBAAA;QAAA,OAC7BF,IAAI,CAACjC,OAAO,CAAC4B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnD,UAAU,CAACkD,WAAW,CAAC,CAAC,CAAC,MAAAM,eAAA,GAC7DD,IAAI,CAAC7C,SAAS,cAAA8C,eAAA,uBAAdA,eAAA,CAAgBN,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnD,UAAU,CAACkD,WAAW,CAAC,CAAC,CAAC,OAAAO,qBAAA,GAChEF,IAAI,CAACG,kBAAkB,cAAAD,qBAAA,uBAAvBA,qBAAA,CAAyBP,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACnD,UAAU,CAACkD,WAAW,CAAC,CAAC,CAAC;MAAA,CAC3E,CAAC;IACH;IAEA,IAAI5C,OAAO,CAACI,SAAS,EAAE;MACrBkC,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACU,IAAI,IAAIA,IAAI,CAAC7C,SAAS,KAAKJ,OAAO,CAACI,SAAS,CAAC;IAC1E;IAEAL,yBAAyB,CAACuC,QAAQ,CAAC;EACrC,CAAC;;EAED;EACA,MAAMe,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3CpE,YAAY,CAACoE,QAAQ,CAAC;IACtBjD,cAAc,CAAC,CAAC,CAAC;IACjBX,aAAa,CAAC,EAAE,CAAC;IACjBM,UAAU,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,SAAS,EAAE,EAAE;MAAEC,SAAS,EAAE;IAAG,CAAC,CAAC;EACzD,CAAC;;EAED;EACA,MAAMoD,gBAAgB,GAAGA,CAAA,KAAM;IAC7B7C,aAAa,CAAC,QAAQ,CAAC;IACvBE,eAAe,CAAC,IAAI,CAAC;IACrBE,WAAW,CAAC;MACVC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,EAAE;MAChBC,YAAY,EAAE,EAAE;MAChBC,kBAAkB,EAAE,EAAE;MACtBC,iBAAiB,EAAE,IAAI;MACvBC,iBAAiB,EAAE,EAAE;MACrBC,iBAAiB,EAAE,IAAI;MACvBC,IAAI,EAAE;IACR,CAAC,CAAC;IACFd,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMgD,WAAW,GAAGA,CAAA,KAAM;IACxBhD,aAAa,CAAC,KAAK,CAAC;IACpBI,eAAe,CAAC,IAAI,CAAC;IACrBF,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;;EAED;EACA,MAAM+C,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACzC7C,WAAW,CAAC8C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACF,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,gBAAgB,GAAItB,IAAI,IAAK;IACjCzB,WAAW,CAAC8C,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP7C,OAAO,EAAEwB,IAAI,CAACxB,OAAO;MACrBG,kBAAkB,EAAEqB,IAAI,CAACuB,eAAe,IAAIvB,IAAI,CAACwB,aAAa,IAAI;IACpE,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC7C,IAAI;MACF,IAAI,CAACnD,QAAQ,CAACE,OAAO,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,YAAY,IAAI,CAACJ,QAAQ,CAACO,iBAAiB,EAAE;QACxGvC,OAAO,CAAC,mCAAmC,CAAC;QAC5C;MACF;MAEAG,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMd,qBAAqB,CAAC+F,oBAAoB,CAACtF,UAAU,EAAEkC,QAAQ,CAAC;MACtEjC,SAAS,CAAC,oCAAoC,CAAC;MAC/C4E,WAAW,CAAC,CAAC;MACb,MAAM5B,kBAAkB,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdlD,OAAO,CAAC,+CAA+C,IAAIkD,KAAK,CAACmC,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACpG,CAAC,SAAS;MACRlF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmF,iBAAiB,GAAG,MAAOC,cAAc,IAAK;IAClD,IAAI;MACFpF,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqF,QAAQ,GAAG,MAAMnG,qBAAqB,CAACoG,WAAW,CAAC3F,UAAU,EAAEyF,cAAc,CAACG,iBAAiB,CAAC;MAEtG,IAAIF,QAAQ,CAACG,QAAQ,EAAE;QACrBC,MAAM,CAACC,IAAI,CAACL,QAAQ,CAACG,QAAQ,EAAE,QAAQ,CAAC;QACxC5F,SAAS,CAAC,2BAA2B,CAAC;MACxC,CAAC,MAAM;QACLC,OAAO,CAAC,kCAAkC,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOkD,KAAK,EAAE;MACdlD,OAAO,CAAC,oCAAoC,IAAIkD,KAAK,CAACmC,OAAO,IAAI,oBAAoB,CAAC,CAAC;IACzF,CAAC,SAAS;MACRlF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2F,0BAA0B,GAAG,MAAOP,cAAc,IAAK;IAC3D,IAAIK,MAAM,CAACG,OAAO,CAAC,mDAAmDR,cAAc,CAACjB,kBAAkB,GAAG,CAAC,EAAE;MAC3G,IAAI;QACFnE,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMd,qBAAqB,CAAC2G,oBAAoB,CAAClG,UAAU,EAAEyF,cAAc,CAACG,iBAAiB,CAAC;QAC9F3F,SAAS,CAAC,uCAAuC,CAAC;QAClD,MAAMgD,kBAAkB,CAAC,CAAC;MAC5B,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdlD,OAAO,CAAC,kDAAkD,IAAIkD,KAAK,CAACmC,OAAO,IAAI,oBAAoB,CAAC,CAAC;MACvG,CAAC,SAAS;QACRlF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC;;EAED;EACA5E,mBAAmB,CAAC0E,GAAG,EAAE,OAAO;IAC9BgG,kBAAkB,EAAGC,MAAM,IAAK;MAC9B,IAAIA,MAAM,KAAK,oBAAoB,EAAE;QACnCxB,gBAAgB,CAAC,CAAC;MACpB,CAAC,MAAM,IAAIwB,MAAM,KAAK,0BAA0B,EAAE;QAChD7F,YAAY,CAAC,CAAC,CAAC;MACjB;IACF;EACF,CAAC,CAAC,CAAC;;EAEH;EACA,MAAM8F,mBAAmB,GAAIC,KAAK,IAAK;IACrC,MAAMC,UAAU,GAAG,CAAC9E,WAAW,GAAG,CAAC,IAAIE,YAAY;IACnD,MAAM6E,QAAQ,GAAGD,UAAU,GAAG5E,YAAY;IAC1C,OAAO2E,KAAK,CAACG,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC1C,CAAC;EAED,MAAME,aAAa,GAAIJ,KAAK,IAAKK,IAAI,CAACC,IAAI,CAACN,KAAK,CAACO,MAAM,GAAGlF,YAAY,CAAC;;EAEvE;EACA,MAAMmF,eAAe,GAAGA,CAACC,KAAK,EAAEhC,KAAK,KAAK;IACxC,OAAO,CAAC,GAAG,IAAIiC,GAAG,CAACD,KAAK,CAACE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACnC,KAAK,CAAC,CAAC,CAACpB,MAAM,CAACwD,OAAO,CAAC,CAAC,CAAC;EACrE,CAAC;;EAED;EACA,MAAMC,sBAAsB,GAAGA,CAAA,kBAC7B1H,OAAA,CAAC7D,KAAK;IAACwL,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,eACzB9H,OAAA,CAAC5D,IAAI;MAAC2L,SAAS;MAACC,OAAO,EAAE,CAAE;MAACC,UAAU,EAAC,QAAQ;MAAAH,QAAA,gBAC7C9H,OAAA,CAAC5D,IAAI;QAACoL,IAAI;QAACU,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACvB9H,OAAA,CAACrD,SAAS;UACRyL,SAAS;UACTC,WAAW,EAAC,6CAA6C;UACzD/C,KAAK,EAAElE,UAAW;UAClBkH,QAAQ,EAAGC,CAAC,IAAKlH,aAAa,CAACkH,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;UAC/CmD,UAAU,EAAE;YACVC,cAAc,eACZ1I,OAAA,CAACnC,cAAc;cAAC8K,QAAQ,EAAC,OAAO;cAAAb,QAAA,eAC9B9H,OAAA,CAAC1B,UAAU;gBAAAsK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACjB;YACDC,YAAY,EAAE5H,UAAU,iBACtBpB,OAAA,CAACnC,cAAc;cAAC8K,QAAQ,EAAC,KAAK;cAAAb,QAAA,eAC5B9H,OAAA,CAACvC,UAAU;gBAACwL,IAAI,EAAC,OAAO;gBAACC,OAAO,EAAEA,CAAA,KAAM7H,aAAa,CAAC,EAAE,CAAE;gBAAAyG,QAAA,eACxD9H,OAAA,CAACV,SAAS;kBAAAsJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAEpB;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAENnI,SAAS,KAAK,CAAC,iBACdZ,OAAA,CAAAE,SAAA;QAAA4H,QAAA,gBACE9H,OAAA,CAAC5D,IAAI;UAACoL,IAAI;UAACU,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAL,QAAA,eACvB9H,OAAA,CAACpD,WAAW;YAACwL,SAAS;YAACa,IAAI,EAAC,OAAO;YAAAnB,QAAA,gBACjC9H,OAAA,CAACnD,UAAU;cAAAiL,QAAA,EAAC;YAAK;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC9B/I,OAAA,CAAClD,MAAM;cACLwI,KAAK,EAAE5D,OAAO,CAACE,KAAM;cACrB0G,QAAQ,EAAGC,CAAC,IAAK5G,UAAU,CAAC4D,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE3D,KAAK,EAAE2G,CAAC,CAACC,MAAM,CAAClD;cAAM,CAAC,CAAC,CAAE;cAC1E6D,KAAK,EAAC,OAAO;cAAArB,QAAA,gBAEb9H,OAAA,CAACjD,QAAQ;gBAACuI,KAAK,EAAC,EAAE;gBAAAwC,QAAA,EAAC;cAAK;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EAClC3B,eAAe,CAACpG,IAAI,EAAE,qBAAqB,CAAC,CAACuG,GAAG,CAAC3F,KAAK,iBACrD5B,OAAA,CAACjD,QAAQ;gBAAauI,KAAK,EAAE1D,KAAM;gBAAAkG,QAAA,EAAElG;cAAK,GAA3BA,KAAK;gBAAAgH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiC,CACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACP/I,OAAA,CAAC5D,IAAI;UAACoL,IAAI;UAACU,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAL,QAAA,eACvB9H,OAAA,CAACpD,WAAW;YAACwL,SAAS;YAACa,IAAI,EAAC,OAAO;YAAAnB,QAAA,gBACjC9H,OAAA,CAACnD,UAAU;cAAAiL,QAAA,EAAC;YAAS;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClC/I,OAAA,CAAClD,MAAM;cACLwI,KAAK,EAAE5D,OAAO,CAACG,SAAU;cACzByG,QAAQ,EAAGC,CAAC,IAAK5G,UAAU,CAAC4D,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE1D,SAAS,EAAE0G,CAAC,CAACC,MAAM,CAAClD;cAAM,CAAC,CAAC,CAAE;cAC9E6D,KAAK,EAAC,WAAW;cAAArB,QAAA,gBAEjB9H,OAAA,CAACjD,QAAQ;gBAACuI,KAAK,EAAC,EAAE;gBAAAwC,QAAA,EAAC;cAAK;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EAClC3B,eAAe,CAACpG,IAAI,EAAE,WAAW,CAAC,CAACuG,GAAG,CAAC1F,SAAS,iBAC/C7B,OAAA,CAACjD,QAAQ;gBAAiBuI,KAAK,EAAEzD,SAAU;gBAAAiG,QAAA,EAAEjG;cAAS,GAAvCA,SAAS;gBAAA+G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAyC,CAClE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA,eACP,CACH,EAEAnI,SAAS,KAAK,CAAC,iBACdZ,OAAA,CAAC5D,IAAI;QAACoL,IAAI;QAACU,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAL,QAAA,eACvB9H,OAAA,CAACpD,WAAW;UAACwL,SAAS;UAACa,IAAI,EAAC,OAAO;UAAAnB,QAAA,gBACjC9H,OAAA,CAACnD,UAAU;YAAAiL,QAAA,EAAC;UAAS;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAClC/I,OAAA,CAAClD,MAAM;YACLwI,KAAK,EAAE5D,OAAO,CAACI,SAAU;YACzBwG,QAAQ,EAAGC,CAAC,IAAK5G,UAAU,CAAC4D,IAAI,KAAK;cAAE,GAAGA,IAAI;cAAEzD,SAAS,EAAEyG,CAAC,CAACC,MAAM,CAAClD;YAAM,CAAC,CAAC,CAAE;YAC9E6D,KAAK,EAAC,WAAW;YAAArB,QAAA,gBAEjB9H,OAAA,CAACjD,QAAQ;cAACuI,KAAK,EAAC,EAAE;cAAAwC,QAAA,EAAC;YAAK;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,EAClC3B,eAAe,CAACtG,cAAc,EAAE,WAAW,CAAC,CAACyG,GAAG,CAACzF,SAAS,iBACzD9B,OAAA,CAACjD,QAAQ;cAAiBuI,KAAK,EAAExD,SAAU;cAAAgG,QAAA,EAAEhG;YAAS,GAAvCA,SAAS;cAAA8G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAyC,CAClE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACP,eAED/I,OAAA,CAAC5D,IAAI;QAACoL,IAAI;QAACU,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACR,EAAE,EAAE;UAAEyB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,UAAU;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAxB,QAAA,gBACpF9H,OAAA,CAAC9D,MAAM;UACLqN,OAAO,EAAC,UAAU;UAClBC,SAAS,eAAExJ,OAAA,CAACV,SAAS;YAAAsJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBG,OAAO,EAAEA,CAAA,KAAM;YACb7H,aAAa,CAAC,EAAE,CAAC;YACjBM,UAAU,CAAC;cAAEC,KAAK,EAAE,EAAE;cAAEC,SAAS,EAAE,EAAE;cAAEC,SAAS,EAAE;YAAG,CAAC,CAAC;UACzD,CAAE;UAAAgG,QAAA,EACH;QAED;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRnI,SAAS,KAAK,CAAC,iBACdZ,OAAA,CAAC9D,MAAM;UACLqN,OAAO,EAAC,WAAW;UACnBC,SAAS,eAAExJ,OAAA,CAAC5B,OAAO;YAAAwK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBG,OAAO,EAAEhE,gBAAiB;UAAA4C,QAAA,EAC3B;QAED;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CACR;;EAED;EACA,MAAMU,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,YAAY,GAAG/C,mBAAmB,CAACrF,YAAY,CAAC;IAEtD,IAAIA,YAAY,CAAC6F,MAAM,KAAK,CAAC,EAAE;MAC7B,oBACEnH,OAAA,CAAC9C,KAAK;QAACyM,QAAQ,EAAC,MAAM;QAAA7B,QAAA,EACnB1G,UAAU,IAAIM,OAAO,CAACE,KAAK,IAAIF,OAAO,CAACG,SAAS,GAC7C,4CAA4C,GAC5C;MAAyB;QAAA+G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAEZ;IAEA,oBACE/I,OAAA,CAAAE,SAAA;MAAA4H,QAAA,gBACE9H,OAAA,CAAC1C,cAAc;QAACsM,SAAS,EAAEzN,KAAM;QAAA2L,QAAA,eAC/B9H,OAAA,CAAC7C,KAAK;UAAC8L,IAAI,EAAC,OAAO;UAAAnB,QAAA,gBACjB9H,OAAA,CAACzC,SAAS;YAAAuK,QAAA,eACR9H,OAAA,CAACxC,QAAQ;cAAAsK,QAAA,gBACP9H,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAC;cAAO;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAC;cAAS;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAC;cAAO;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAC;cAAQ;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/B/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAC;cAAM;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC7B/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAC;cAAK;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5B/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAC;cAAK;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC5B/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAC;cAAW;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClC/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAC;cAAM;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ/I,OAAA,CAAC5C,SAAS;YAAA0K,QAAA,EACP4B,YAAY,CAACnC,GAAG,CAAErD,IAAI,IAAK;cAC1B,MAAM2F,aAAa,GAAG/I,cAAc,CAACgJ,IAAI,CAACnF,IAAI,IAAIA,IAAI,CAACjC,OAAO,KAAKwB,IAAI,CAACxB,OAAO,CAAC;cAChF,oBACE1C,OAAA,CAACxC,QAAQ;gBAAAsK,QAAA,gBACP9H,OAAA,CAAC3C,SAAS;kBAAAyK,QAAA,eACR9H,OAAA,CAAC/D,UAAU;oBAACsN,OAAO,EAAC,OAAO;oBAACQ,UAAU,EAAC,QAAQ;oBAAAjC,QAAA,EAC5C5D,IAAI,CAACxB;kBAAO;oBAAAkG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZ/I,OAAA,CAAC3C,SAAS;kBAAAyK,QAAA,EAAE5D,IAAI,CAACrC;gBAAS;kBAAA+G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvC/I,OAAA,CAAC3C,SAAS;kBAAAyK,QAAA,EAAE5D,IAAI,CAAC8F;gBAAO;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrC/I,OAAA,CAAC3C,SAAS;kBAAAyK,QAAA,EAAE5D,IAAI,CAACM;gBAAmB;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjD/I,OAAA,CAAC3C,SAAS;kBAAAyK,QAAA,EAAE5D,IAAI,CAACO;gBAAiB;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/C/I,OAAA,CAAC3C,SAAS;kBAAAyK,QAAA,GAAE5D,IAAI,CAACuB,eAAe,IAAIvB,IAAI,CAACwB,aAAa,EAAC,IAAE;gBAAA;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACrE/I,OAAA,CAAC3C,SAAS;kBAAAyK,QAAA,eACR9H,OAAA,CAAChC,IAAI;oBACHiL,IAAI,EAAC,OAAO;oBACZE,KAAK,EAAEjF,IAAI,CAACQ,mBAAoB;oBAChCuF,KAAK,EAAE/F,IAAI,CAACQ,mBAAmB,KAAK,YAAY,GAAG,SAAS,GAAG;kBAAU;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZ/I,OAAA,CAAC3C,SAAS;kBAAAyK,QAAA,EACP+B,aAAa,gBACZ7J,OAAA,CAAChC,IAAI;oBACHiL,IAAI,EAAC,OAAO;oBACZiB,IAAI,eAAElK,OAAA,CAACN,SAAS;sBAAAkJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACpBI,KAAK,EAAC,aAAa;oBACnBc,KAAK,EAAC;kBAAS;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,gBAEF/I,OAAA,CAAChC,IAAI;oBACHiL,IAAI,EAAC,OAAO;oBACZiB,IAAI,eAAElK,OAAA,CAACJ,WAAW;sBAAAgJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACtBI,KAAK,EAAC,iBAAiB;oBACvBc,KAAK,EAAC;kBAAS;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB;gBACF;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC,eACZ/I,OAAA,CAAC3C,SAAS;kBAAAyK,QAAA,eACR9H,OAAA,CAAC/B,OAAO;oBAACkM,KAAK,EAAC,qBAAqB;oBAAArC,QAAA,eAClC9H,OAAA,CAACvC,UAAU;sBACTwL,IAAI,EAAC,OAAO;sBACZC,OAAO,EAAEA,CAAA,KAAM;wBACb1D,gBAAgB,CAACtB,IAAI,CAAC;wBACtBgB,gBAAgB,CAAC,CAAC;sBACpB,CAAE;sBACFkF,QAAQ,EAAEP,aAAc;sBAAA/B,QAAA,eAExB9H,OAAA,CAAC5B,OAAO;wBAAAwK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA,GAhDC7E,IAAI,CAACxB,OAAO;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAiDjB,CAAC;YAEf,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhB/B,aAAa,CAAC1F,YAAY,CAAC,GAAG,CAAC,iBAC9BtB,OAAA,CAAChE,GAAG;QAAC2L,EAAE,EAAE;UAAEyB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEgB,EAAE,EAAE;QAAE,CAAE;QAAAvC,QAAA,eAC5D9H,OAAA,CAACpC,UAAU;UACT0M,KAAK,EAAEtD,aAAa,CAAC1F,YAAY,CAAE;UACnCiJ,IAAI,EAAExI,WAAY;UAClBuG,QAAQ,EAAEA,CAACtD,KAAK,EAAEM,KAAK,KAAKtD,cAAc,CAACsD,KAAK,CAAE;UAClD2E,KAAK,EAAC;QAAS;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA,eACD,CAAC;EAEP,CAAC;;EAED;EACA,MAAMyB,yBAAyB,GAAGA,CAAA,KAAM;IACtC,MAAMd,YAAY,GAAG/C,mBAAmB,CAACnF,sBAAsB,CAAC;IAEhE,IAAIA,sBAAsB,CAAC2F,MAAM,KAAK,CAAC,EAAE;MACvC,oBACEnH,OAAA,CAAC9C,KAAK;QAACyM,QAAQ,EAAC,MAAM;QAAA7B,QAAA,EACnB1G,UAAU,IAAIM,OAAO,CAACI,SAAS,GAC5B,uDAAuD,GACvD;MAAoC;QAAA8G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAEZ;IAEA,oBACE/I,OAAA,CAAAE,SAAA;MAAA4H,QAAA,gBACE9H,OAAA,CAAC1C,cAAc;QAACsM,SAAS,EAAEzN,KAAM;QAAA2L,QAAA,eAC/B9H,OAAA,CAAC7C,KAAK;UAAC8L,IAAI,EAAC,OAAO;UAAAnB,QAAA,gBACjB9H,OAAA,CAACzC,SAAS;YAAAuK,QAAA,eACR9H,OAAA,CAACxC,QAAQ;cAAAsK,QAAA,gBACP9H,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAC;cAAc;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACrC/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAC;cAAO;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC9B/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAC;cAAI;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC3B/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAC;cAAS;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAC;cAAS;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAC;cAAS;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAChC/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAC;cAAU;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAC;cAAM;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ/I,OAAA,CAAC5C,SAAS;YAAA0K,QAAA,EACP4B,YAAY,CAACnC,GAAG,CAAE5C,IAAI,iBACrB3E,OAAA,CAACxC,QAAQ;cAAAsK,QAAA,gBACP9H,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,eACR9H,OAAA,CAAC/D,UAAU;kBAACsN,OAAO,EAAC,OAAO;kBAACQ,UAAU,EAAC,QAAQ;kBAAAjC,QAAA,EAC5CnD,IAAI,CAACG;gBAAkB;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACZ/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAEnD,IAAI,CAACjC;cAAO;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrC/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAE,IAAI2C,IAAI,CAAC9F,IAAI,CAAC+F,mBAAmB,CAAC,CAACC,kBAAkB,CAAC;cAAC;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChF/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAEnD,IAAI,CAAC7C,SAAS,IAAI6C,IAAI,CAAChC;cAAY;gBAAAiG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC5D/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,EAAEnD,IAAI,CAACiG,SAAS,IAAI;cAAK;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChD/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,GAAEnD,IAAI,CAAC9B,kBAAkB,EAAC,IAAE;cAAA;gBAAA+F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAClD/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,eACR9H,OAAA,CAAChC,IAAI;kBACHiL,IAAI,EAAC,OAAO;kBACZE,KAAK,EAAE,GAAGxE,IAAI,CAAC5B,iBAAiB,KAAM;kBACtCkH,KAAK,EAAEY,UAAU,CAAClG,IAAI,CAAC5B,iBAAiB,CAAC,IAAI,GAAG,GAAG,SAAS,GAAG;gBAAU;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZ/I,OAAA,CAAC3C,SAAS;gBAAAyK,QAAA,eACR9H,OAAA,CAACjC,KAAK;kBAAC+M,SAAS,EAAC,KAAK;kBAAC9C,OAAO,EAAE,GAAI;kBAAAF,QAAA,gBAClC9H,OAAA,CAAC/B,OAAO;oBAACkM,KAAK,EAAC,qBAAqB;oBAAArC,QAAA,eAClC9H,OAAA,CAACvC,UAAU;sBACTwL,IAAI,EAAC,OAAO;sBACZC,OAAO,EAAEA,CAAA,KAAM;wBACb3G,eAAe,CAACoC,IAAI,CAAC;wBACrBtC,aAAa,CAAC,MAAM,CAAC;wBACrBF,aAAa,CAAC,IAAI,CAAC;sBACrB,CAAE;sBAAA2F,QAAA,eAEF9H,OAAA,CAAClB,QAAQ;wBAAA8J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACV/I,OAAA,CAAC/B,OAAO;oBAACkM,KAAK,EAAC,YAAY;oBAAArC,QAAA,eACzB9H,OAAA,CAACvC,UAAU;sBACTwL,IAAI,EAAC,OAAO;sBACZC,OAAO,EAAEA,CAAA,KAAMpD,iBAAiB,CAACnB,IAAI,CAAE;sBACvCyF,QAAQ,EAAE1J,OAAQ;sBAAAoH,QAAA,eAElB9H,OAAA,CAACtB,OAAO;wBAAAkK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACV/I,OAAA,CAAC/B,OAAO;oBAACkM,KAAK,EAAC,SAAS;oBAAArC,QAAA,eACtB9H,OAAA,CAACvC,UAAU;sBACTwL,IAAI,EAAC,OAAO;sBACZgB,KAAK,EAAC,OAAO;sBACbf,OAAO,EAAEA,CAAA,KAAM5C,0BAA0B,CAAC3B,IAAI,CAAE;sBAAAmD,QAAA,eAEhD9H,OAAA,CAAChB,UAAU;wBAAA4J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA,GAnDCpE,IAAI,CAACuB,iBAAiB;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoD3B,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhB/B,aAAa,CAACxF,sBAAsB,CAAC,GAAG,CAAC,iBACxCxB,OAAA,CAAChE,GAAG;QAAC2L,EAAE,EAAE;UAAEyB,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,QAAQ;UAAEgB,EAAE,EAAE;QAAE,CAAE;QAAAvC,QAAA,eAC5D9H,OAAA,CAACpC,UAAU;UACT0M,KAAK,EAAEtD,aAAa,CAACxF,sBAAsB,CAAE;UAC7C+I,IAAI,EAAExI,WAAY;UAClBuG,QAAQ,EAAEA,CAACtD,KAAK,EAAEM,KAAK,KAAKtD,cAAc,CAACsD,KAAK,CAAE;UAClD2E,KAAK,EAAC;QAAS;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA,eACD,CAAC;EAEP,CAAC;;EAED;EACA,MAAMgC,0BAA0B,GAAGA,CAAA,KAAM;IACvC,IAAI3I,UAAU,KAAK,QAAQ,IAAIA,UAAU,KAAK,MAAM,EAAE,OAAO,IAAI;IAEjE,oBACEpC,OAAA,CAACzD,MAAM;MAAC8J,IAAI,EAAEnE,UAAW;MAAC8I,OAAO,EAAE7F,WAAY;MAAC8F,QAAQ,EAAC,IAAI;MAAC7C,SAAS;MAAAN,QAAA,gBACrE9H,OAAA,CAACxD,WAAW;QAAAsL,QAAA,EACT1F,UAAU,KAAK,QAAQ,GAAG,sBAAsB,GAAG;MAAyB;QAAAwG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eACd/I,OAAA,CAACvD,aAAa;QAAAqL,QAAA,eACZ9H,OAAA,CAAC5D,IAAI;UAAC2L,SAAS;UAACC,OAAO,EAAE,CAAE;UAACL,EAAE,EAAE;YAAE0C,EAAE,EAAE;UAAE,CAAE;UAAAvC,QAAA,gBACxC9H,OAAA,CAAC5D,IAAI;YAACoL,IAAI;YAACU,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAL,QAAA,eACvB9H,OAAA,CAAChD,YAAY;cACXkO,OAAO,EAAElK,IAAI,CAACiD,MAAM,CAACC,IAAI,IACvB,CAACpD,cAAc,CAACgJ,IAAI,CAACnF,IAAI,IAAIA,IAAI,CAACjC,OAAO,KAAKwB,IAAI,CAACxB,OAAO,CAAC,IAC3DwB,IAAI,CAACxB,OAAO,KAAKF,QAAQ,CAACE,OAC5B,CAAE;cACFyI,cAAc,EAAGzE,MAAM,IAAK,GAAGA,MAAM,CAAChE,OAAO,MAAMgE,MAAM,CAAC7E,SAAS,EAAG;cACtEyD,KAAK,EAAEtE,IAAI,CAACoK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3I,OAAO,KAAKF,QAAQ,CAACE,OAAO,CAAC,IAAI,IAAK;cAC9D4F,QAAQ,EAAEA,CAACtD,KAAK,EAAEC,QAAQ,KAAK;gBAC7B,IAAIA,QAAQ,EAAE;kBACZO,gBAAgB,CAACP,QAAQ,CAAC;gBAC5B,CAAC,MAAM;kBACLxC,WAAW,CAAC8C,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE7C,OAAO,EAAE,EAAE;oBAAEG,kBAAkB,EAAE;kBAAG,CAAC,CAAC,CAAC;gBACzE;cACF,CAAE;cACFyI,WAAW,EAAGC,MAAM,iBAClBvL,OAAA,CAACrD,SAAS;gBAAA,GACJ4O,MAAM;gBACVpC,KAAK,EAAC,QAAQ;gBACdd,WAAW,EAAC,mBAAmB;gBAC/BmD,QAAQ;cAAA;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CACD;cACF0C,YAAY,EAAEA,CAACC,KAAK,EAAEhF,MAAM,kBAC1B1G,OAAA,CAAChE,GAAG;gBAAC4N,SAAS,EAAC,IAAI;gBAAA,GAAK8B,KAAK;gBAAA5D,QAAA,eAC3B9H,OAAA,CAAChE,GAAG;kBAAA8L,QAAA,gBACF9H,OAAA,CAAC/D,UAAU;oBAACsN,OAAO,EAAC,OAAO;oBAACQ,UAAU,EAAC,QAAQ;oBAAAjC,QAAA,EAC5CpB,MAAM,CAAChE;kBAAO;oBAAAkG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACb/I,OAAA,CAAC/D,UAAU;oBAACsN,OAAO,EAAC,SAAS;oBAACU,KAAK,EAAC,gBAAgB;oBAAAnC,QAAA,GACjDpB,MAAM,CAAC7E,SAAS,EAAC,KAAG,EAAC6E,MAAM,CAAClC,mBAAmB,EAAC,UAAG,EAACkC,MAAM,CAACjC,iBAAiB;kBAAA;oBAAAmE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACL;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP/I,OAAA,CAAC5D,IAAI;YAACoL,IAAI;YAACU,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAL,QAAA,eACvB9H,OAAA,CAACrD,SAAS;cACRyL,SAAS;cACTe,KAAK,EAAC,aAAa;cACnB7D,KAAK,EAAE9C,QAAQ,CAACG,YAAa;cAC7B2F,QAAQ,EAAGC,CAAC,IAAKnD,gBAAgB,CAAC,cAAc,EAAEmD,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;cAClEkG,QAAQ;YAAA;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP/I,OAAA,CAAC5D,IAAI;YAACoL,IAAI;YAACU,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAL,QAAA,eACvB9H,OAAA,CAACpD,WAAW;cAACwL,SAAS;cAACoD,QAAQ;cAAA1D,QAAA,gBAC7B9H,OAAA,CAACnD,UAAU;gBAAAiL,QAAA,EAAC;cAAW;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACpC/I,OAAA,CAAClD,MAAM;gBACLwI,KAAK,EAAE9C,QAAQ,CAACI,YAAa;gBAC7B0F,QAAQ,EAAGC,CAAC,IAAKnD,gBAAgB,CAAC,cAAc,EAAEmD,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;gBAClE6D,KAAK,EAAC,aAAa;gBAAArB,QAAA,EAElB5G,SAAS,CAACqG,GAAG,CAAEqD,SAAS,iBACvB5K,OAAA,CAACjD,QAAQ;kBAA8BuI,KAAK,EAAEsF,SAAS,CAAChI,YAAa;kBAAAkF,QAAA,GAClE8C,SAAS,CAACe,IAAI,EAAC,KAAG,EAACf,SAAS,CAACgB,KAAK,EAAC,GAAC,EAAChB,SAAS,CAACiB,OAAO;gBAAA,GAD1CjB,SAAS,CAAChI,YAAY;kBAAAgG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAE3B,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEP/I,OAAA,CAAC5D,IAAI;YAACoL,IAAI;YAACU,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAL,QAAA,eACvB9H,OAAA,CAACrD,SAAS;cACRyL,SAAS;cACTe,KAAK,EAAC,0BAA0B;cAChC2C,IAAI,EAAC,QAAQ;cACbxG,KAAK,EAAE9C,QAAQ,CAACK,kBAAmB;cACnCyF,QAAQ,EAAGC,CAAC,IAAKnD,gBAAgB,CAAC,oBAAoB,EAAEmD,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;cACxEkG,QAAQ;YAAA;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP/I,OAAA,CAAC5D,IAAI;YAACoL,IAAI;YAACU,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAL,QAAA,eACvB9H,OAAA,CAACpD,WAAW;cAACwL,SAAS;cAAAN,QAAA,gBACpB9H,OAAA,CAACnD,UAAU;gBAAAiL,QAAA,EAAC;cAAU;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnC/I,OAAA,CAAClD,MAAM;gBACLwI,KAAK,EAAE9C,QAAQ,CAACM,iBAAkB;gBAClCwF,QAAQ,EAAGC,CAAC,IAAKnD,gBAAgB,CAAC,mBAAmB,EAAEmD,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;gBACvE6D,KAAK,EAAC,eAAY;gBAAArB,QAAA,gBAElB9H,OAAA,CAACjD,QAAQ;kBAACuI,KAAK,EAAC,IAAI;kBAAAwC,QAAA,EAAC;gBAAE;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClC/I,OAAA,CAACjD,QAAQ;kBAACuI,KAAK,EAAC,KAAK;kBAAAwC,QAAA,EAAC;gBAAG;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEP/I,OAAA,CAAC5D,IAAI;YAACoL,IAAI;YAACU,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAL,QAAA,eACvB9H,OAAA,CAACrD,SAAS;cACRyL,SAAS;cACTe,KAAK,EAAC,wBAAmB;cACzB2C,IAAI,EAAC,QAAQ;cACbxG,KAAK,EAAE9C,QAAQ,CAACO,iBAAkB;cAClCuF,QAAQ,EAAGC,CAAC,IAAKnD,gBAAgB,CAAC,mBAAmB,EAAEmD,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;cACvEkG,QAAQ;cACRO,UAAU,EAAC;YAAmC;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEP/I,OAAA,CAAC5D,IAAI;YAACoL,IAAI;YAACU,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAL,QAAA,eACvB9H,OAAA,CAACpD,WAAW;cAACwL,SAAS;cAAAN,QAAA,gBACpB9H,OAAA,CAACnD,UAAU;gBAAAiL,QAAA,EAAC;cAAU;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnC/I,OAAA,CAAClD,MAAM;gBACLwI,KAAK,EAAE9C,QAAQ,CAACQ,iBAAkB;gBAClCsF,QAAQ,EAAGC,CAAC,IAAKnD,gBAAgB,CAAC,mBAAmB,EAAEmD,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAE;gBACvE6D,KAAK,EAAC,YAAY;gBAAArB,QAAA,gBAElB9H,OAAA,CAACjD,QAAQ;kBAACuI,KAAK,EAAC,IAAI;kBAAAwC,QAAA,EAAC;gBAAE;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAClC/I,OAAA,CAACjD,QAAQ;kBAACuI,KAAK,EAAC,KAAK;kBAAAwC,QAAA,EAAC;gBAAG;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEP/I,OAAA,CAAC5D,IAAI;YAACoL,IAAI;YAACU,EAAE,EAAE,EAAG;YAAAJ,QAAA,eAChB9H,OAAA,CAACrD,SAAS;cACRyL,SAAS;cACTe,KAAK,EAAC,MAAM;cACZ6C,SAAS;cACTC,IAAI,EAAE,CAAE;cACR3G,KAAK,EAAE9C,QAAQ,CAACS,IAAK;cACrBqF,QAAQ,EAAGC,CAAC,IAAKnD,gBAAgB,CAAC,MAAM,EAAEmD,CAAC,CAACC,MAAM,CAAClD,KAAK;YAAE;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChB/I,OAAA,CAACtD,aAAa;QAAAoL,QAAA,gBACZ9H,OAAA,CAAC9D,MAAM;UAACgN,OAAO,EAAE/D,WAAY;UAAA2C,QAAA,EAAC;QAAO;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9C/I,OAAA,CAAC9D,MAAM;UACLgN,OAAO,EAAEvD,0BAA2B;UACpC4D,OAAO,EAAC,WAAW;UACnBa,QAAQ,EAAE1J,OAAO,IAAI,CAAC8B,QAAQ,CAACE,OAAO,IAAI,CAACF,QAAQ,CAACG,YAAY,IAAI,CAACH,QAAQ,CAACI,YAAY,IAAI,CAACJ,QAAQ,CAACO,iBAAkB;UAC1HyG,SAAS,EAAE9I,OAAO,gBAAGV,OAAA,CAAC/C,gBAAgB;YAACgM,IAAI,EAAE;UAAG;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG/I,OAAA,CAACZ,QAAQ;YAAAwJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAjB,QAAA,EAElE1F,UAAU,KAAK,QAAQ,GAAG,qBAAqB,GAAG;QAAiB;UAAAwG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;EACA,MAAMmD,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI9J,UAAU,KAAK,MAAM,IAAI,CAACE,YAAY,EAAE,OAAO,IAAI;IAEvD,oBACEtC,OAAA,CAACzD,MAAM;MAAC8J,IAAI,EAAEnE,UAAW;MAAC8I,OAAO,EAAE7F,WAAY;MAAC8F,QAAQ,EAAC,IAAI;MAAC7C,SAAS;MAAAN,QAAA,gBACrE9H,OAAA,CAACxD,WAAW;QAAAsL,QAAA,GAAC,4BACe,EAACxF,YAAY,CAACwC,kBAAkB;MAAA;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACd/I,OAAA,CAACvD,aAAa;QAAAqL,QAAA,eACZ9H,OAAA,CAAC5D,IAAI;UAAC2L,SAAS;UAACC,OAAO,EAAE,CAAE;UAACL,EAAE,EAAE;YAAE0C,EAAE,EAAE;UAAE,CAAE;UAAAvC,QAAA,gBACxC9H,OAAA,CAAC5D,IAAI;YAACoL,IAAI;YAACU,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAL,QAAA,eACvB9H,OAAA,CAAC3D,IAAI;cAACkN,OAAO,EAAC,UAAU;cAAAzB,QAAA,eACtB9H,OAAA,CAAC1D,WAAW;gBAAAwL,QAAA,gBACV9H,OAAA,CAAC/D,UAAU;kBAACsN,OAAO,EAAC,IAAI;kBAAC4C,YAAY;kBAAArE,QAAA,EAAC;gBAEtC;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb/I,OAAA,CAAC/D,UAAU;kBAACsN,OAAO,EAAC,OAAO;kBAACU,KAAK,EAAC,gBAAgB;kBAAAnC,QAAA,GAAC,WACxC,eAAA9H,OAAA;oBAAA8H,QAAA,EAASxF,YAAY,CAACI;kBAAO;oBAAAkG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACb/I,OAAA,CAAC/D,UAAU;kBAACsN,OAAO,EAAC,OAAO;kBAACU,KAAK,EAAC,gBAAgB;kBAAAnC,QAAA,GAAC,sBAC7B,eAAA9H,OAAA;oBAAA8H,QAAA,GAASxF,YAAY,CAACO,kBAAkB,EAAC,IAAE;kBAAA;oBAAA+F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEP/I,OAAA,CAAC5D,IAAI;YAACoL,IAAI;YAACU,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAL,QAAA,eACvB9H,OAAA,CAAC3D,IAAI;cAACkN,OAAO,EAAC,UAAU;cAAAzB,QAAA,eACtB9H,OAAA,CAAC1D,WAAW;gBAAAwL,QAAA,gBACV9H,OAAA,CAAC/D,UAAU;kBAACsN,OAAO,EAAC,IAAI;kBAAC4C,YAAY;kBAAArE,QAAA,EAAC;gBAEtC;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb/I,OAAA,CAAC/D,UAAU;kBAACsN,OAAO,EAAC,OAAO;kBAACU,KAAK,EAAC,gBAAgB;kBAAAnC,QAAA,GAAC,UACzC,eAAA9H,OAAA;oBAAA8H,QAAA,EAASxF,YAAY,CAACwC;kBAAkB;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACb/I,OAAA,CAAC/D,UAAU;kBAACsN,OAAO,EAAC,OAAO;kBAACU,KAAK,EAAC,gBAAgB;kBAAAnC,QAAA,GAAC,QAC3C,eAAA9H,OAAA;oBAAA8H,QAAA,EAAS,IAAI2C,IAAI,CAACnI,YAAY,CAACoI,mBAAmB,CAAC,CAACC,kBAAkB,CAAC;kBAAC;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9E,CAAC,eACb/I,OAAA,CAAC/D,UAAU;kBAACsN,OAAO,EAAC,OAAO;kBAACU,KAAK,EAAC,gBAAgB;kBAAAnC,QAAA,GAAC,aACtC,eAAA9H,OAAA;oBAAA8H,QAAA,EAASxF,YAAY,CAACR,SAAS,IAAIQ,YAAY,CAACK;kBAAY;oBAAAiG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEP/I,OAAA,CAAC5D,IAAI;YAACoL,IAAI;YAACU,EAAE,EAAE,EAAG;YAAAJ,QAAA,eAChB9H,OAAA,CAAC3D,IAAI;cAACkN,OAAO,EAAC,UAAU;cAAAzB,QAAA,eACtB9H,OAAA,CAAC1D,WAAW;gBAAAwL,QAAA,gBACV9H,OAAA,CAAC/D,UAAU;kBAACsN,OAAO,EAAC,IAAI;kBAAC4C,YAAY;kBAAArE,QAAA,EAAC;gBAEtC;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb/I,OAAA,CAAC5D,IAAI;kBAAC2L,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAAF,QAAA,gBACzB9H,OAAA,CAAC5D,IAAI;oBAACoL,IAAI;oBAACU,EAAE,EAAE,CAAE;oBAAAJ,QAAA,gBACf9H,OAAA,CAAC/D,UAAU;sBAACsN,OAAO,EAAC,OAAO;sBAACU,KAAK,EAAC,gBAAgB;sBAAAnC,QAAA,EAAC;oBAEnD;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb/I,OAAA,CAAChC,IAAI;sBACHiL,IAAI,EAAC,OAAO;sBACZE,KAAK,EAAE7G,YAAY,CAACQ,iBAAkB;sBACtCmH,KAAK,EAAE3H,YAAY,CAACQ,iBAAiB,KAAK,IAAI,GAAG,SAAS,GAAG;oBAAQ;sBAAA8F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACP/I,OAAA,CAAC5D,IAAI;oBAACoL,IAAI;oBAACU,EAAE,EAAE,CAAE;oBAAAJ,QAAA,gBACf9H,OAAA,CAAC/D,UAAU;sBAACsN,OAAO,EAAC,OAAO;sBAACU,KAAK,EAAC,gBAAgB;sBAAAnC,QAAA,EAAC;oBAEnD;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb/I,OAAA,CAAChC,IAAI;sBACHiL,IAAI,EAAC,OAAO;sBACZE,KAAK,EAAE,GAAG7G,YAAY,CAACS,iBAAiB,KAAM;sBAC9CkH,KAAK,EAAEY,UAAU,CAACvI,YAAY,CAACS,iBAAiB,CAAC,IAAI,GAAG,GAAG,SAAS,GAAG;oBAAU;sBAAA6F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACP/I,OAAA,CAAC5D,IAAI;oBAACoL,IAAI;oBAACU,EAAE,EAAE,CAAE;oBAAAJ,QAAA,gBACf9H,OAAA,CAAC/D,UAAU;sBAACsN,OAAO,EAAC,OAAO;sBAACU,KAAK,EAAC,gBAAgB;sBAAAnC,QAAA,EAAC;oBAEnD;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb/I,OAAA,CAAChC,IAAI;sBACHiL,IAAI,EAAC,OAAO;sBACZE,KAAK,EAAE7G,YAAY,CAACU,iBAAkB;sBACtCiH,KAAK,EAAE3H,YAAY,CAACU,iBAAiB,KAAK,IAAI,GAAG,SAAS,GAAG;oBAAQ;sBAAA4F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAENzG,YAAY,CAACW,IAAI,iBAChBjD,OAAA,CAAC5D,IAAI;YAACoL,IAAI;YAACU,EAAE,EAAE,EAAG;YAAAJ,QAAA,eAChB9H,OAAA,CAAC3D,IAAI;cAACkN,OAAO,EAAC,UAAU;cAAAzB,QAAA,eACtB9H,OAAA,CAAC1D,WAAW;gBAAAwL,QAAA,gBACV9H,OAAA,CAAC/D,UAAU;kBAACsN,OAAO,EAAC,IAAI;kBAAC4C,YAAY;kBAAArE,QAAA,EAAC;gBAEtC;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb/I,OAAA,CAAC/D,UAAU;kBAACsN,OAAO,EAAC,OAAO;kBAAAzB,QAAA,EACxBxF,YAAY,CAACW;gBAAI;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChB/I,OAAA,CAACtD,aAAa;QAAAoL,QAAA,gBACZ9H,OAAA,CAAC9D,MAAM;UAACgN,OAAO,EAAE/D,WAAY;UAAA2C,QAAA,EAAC;QAAM;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7C/I,OAAA,CAAC9D,MAAM;UACLgN,OAAO,EAAEA,CAAA,KAAMpD,iBAAiB,CAACxD,YAAY,CAAE;UAC/CiH,OAAO,EAAC,WAAW;UACnBC,SAAS,eAAExJ,OAAA,CAACtB,OAAO;YAAAkK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBqB,QAAQ,EAAE1J,OAAQ;UAAAoH,QAAA,EACnB;QAED;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEb,CAAC;;EAED;EACA,MAAMqD,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,SAAS,GAAGrL,IAAI,CAACmG,MAAM;IAC7B,MAAMmF,cAAc,GAAGtL,IAAI,CAACiD,MAAM,CAACoH,CAAC,IAAIA,CAAC,CAAC3G,mBAAmB,KAAK,YAAY,CAAC,CAACyC,MAAM;IACtF,MAAMoF,eAAe,GAAGzL,cAAc,CAACqG,MAAM;IAC7C,MAAMqF,yBAAyB,GAAGH,SAAS,GAAG,CAAC,GAAGpF,IAAI,CAACwF,KAAK,CAAEF,eAAe,GAAGD,cAAc,GAAI,GAAG,CAAC,GAAG,CAAC;IAE1G,oBACEtM,OAAA,CAAC5D,IAAI;MAAC2L,SAAS;MAACC,OAAO,EAAE,CAAE;MAACL,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACxC9H,OAAA,CAAC5D,IAAI;QAACoL,IAAI;QAACU,EAAE,EAAE,EAAG;QAACwE,EAAE,EAAE,CAAE;QAACvE,EAAE,EAAE,CAAE;QAAAL,QAAA,eAC9B9H,OAAA,CAAC3D,IAAI;UAAAyL,QAAA,eACH9H,OAAA,CAAC1D,WAAW;YAAAwL,QAAA,gBACV9H,OAAA,CAAC/D,UAAU;cAACgO,KAAK,EAAC,gBAAgB;cAACkC,YAAY;cAAArE,QAAA,EAAC;YAEhD;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/I,OAAA,CAAC/D,UAAU;cAACsN,OAAO,EAAC,IAAI;cAAAzB,QAAA,EACrBuE;YAAS;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP/I,OAAA,CAAC5D,IAAI;QAACoL,IAAI;QAACU,EAAE,EAAE,EAAG;QAACwE,EAAE,EAAE,CAAE;QAACvE,EAAE,EAAE,CAAE;QAAAL,QAAA,eAC9B9H,OAAA,CAAC3D,IAAI;UAAAyL,QAAA,eACH9H,OAAA,CAAC1D,WAAW;YAAAwL,QAAA,gBACV9H,OAAA,CAAC/D,UAAU;cAACgO,KAAK,EAAC,gBAAgB;cAACkC,YAAY;cAAArE,QAAA,EAAC;YAEhD;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/I,OAAA,CAAC/D,UAAU;cAACsN,OAAO,EAAC,IAAI;cAAAzB,QAAA,EACrBwE;YAAc;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP/I,OAAA,CAAC5D,IAAI;QAACoL,IAAI;QAACU,EAAE,EAAE,EAAG;QAACwE,EAAE,EAAE,CAAE;QAACvE,EAAE,EAAE,CAAE;QAAAL,QAAA,eAC9B9H,OAAA,CAAC3D,IAAI;UAAAyL,QAAA,eACH9H,OAAA,CAAC1D,WAAW;YAAAwL,QAAA,gBACV9H,OAAA,CAAC/D,UAAU;cAACgO,KAAK,EAAC,gBAAgB;cAACkC,YAAY;cAAArE,QAAA,EAAC;YAEhD;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/I,OAAA,CAAC/D,UAAU;cAACsN,OAAO,EAAC,IAAI;cAAAzB,QAAA,EACrByE;YAAe;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACP/I,OAAA,CAAC5D,IAAI;QAACoL,IAAI;QAACU,EAAE,EAAE,EAAG;QAACwE,EAAE,EAAE,CAAE;QAACvE,EAAE,EAAE,CAAE;QAAAL,QAAA,eAC9B9H,OAAA,CAAC3D,IAAI;UAAAyL,QAAA,eACH9H,OAAA,CAAC1D,WAAW;YAAAwL,QAAA,gBACV9H,OAAA,CAAC/D,UAAU;cAACgO,KAAK,EAAC,gBAAgB;cAACkC,YAAY;cAAArE,QAAA,EAAC;YAEhD;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb/I,OAAA,CAAC/D,UAAU;cAACsN,OAAO,EAAC,IAAI;cAACU,KAAK,EAAEuC,yBAAyB,IAAI,EAAE,GAAG,cAAc,GAAG,cAAe;cAAA1E,QAAA,GAC/F0E,yBAAyB,EAAC,GAC7B;YAAA;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEX,CAAC;EAED,oBACE/I,OAAA,CAAChE,GAAG;IAAA8L,QAAA,GACDsE,WAAW,CAAC,CAAC,eAEdpM,OAAA,CAAC7D,KAAK;MAACwL,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eACnB9H,OAAA,CAACtC,IAAI;QACH4H,KAAK,EAAE1E,SAAU;QACjB0H,QAAQ,EAAEvD,eAAgB;QAC1B4H,cAAc,EAAC,SAAS;QACxBC,SAAS,EAAC,SAAS;QAAA9E,QAAA,gBAEnB9H,OAAA,CAACrC,GAAG;UAACwL,KAAK,EAAE,SAAS7H,YAAY,CAAC6F,MAAM;QAAI;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/C/I,OAAA,CAACrC,GAAG;UAACwL,KAAK,EAAE,mBAAmB3H,sBAAsB,CAAC2F,MAAM;QAAI;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAEPrB,sBAAsB,CAAC,CAAC,EAExBhH,OAAO,iBACNV,OAAA,CAAChE,GAAG;MAAC2L,EAAE,EAAE;QAAEyB,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEwD,EAAE,EAAE;MAAE,CAAE;MAAA/E,QAAA,eAC5D9H,OAAA,CAAC/C,gBAAgB;QAAA2L,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,EAEA,CAACrI,OAAO,IAAIE,SAAS,KAAK,CAAC,IAAI6I,eAAe,CAAC,CAAC,EAChD,CAAC/I,OAAO,IAAIE,SAAS,KAAK,CAAC,IAAI4J,yBAAyB,CAAC,CAAC,EAE1DO,0BAA0B,CAAC,CAAC,EAC5BmB,gBAAgB,CAAC,CAAC;EAAA;IAAAtD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChB,CAAC;AAEV,CAAC,kCAAC;AAAC+D,GAAA,GAj7BG3M,0BAA0B;AAm7BhC,eAAeA,0BAA0B;AAAC,IAAAE,EAAA,EAAAyM,GAAA;AAAAC,YAAA,CAAA1M,EAAA;AAAA0M,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}