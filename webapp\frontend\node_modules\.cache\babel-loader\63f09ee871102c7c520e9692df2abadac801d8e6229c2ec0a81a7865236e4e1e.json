{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\CertificazioneCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { Box, Typography, Paper, Button, IconButton, Alert } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Refresh as RefreshIcon, Home as HomeIcon } from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport CertificazioneCaviImproved from '../../components/cavi/CertificazioneCaviImproved';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CertificazioneCaviPage = () => {\n  _s();\n  const {\n    isImpersonating\n  } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const certificazioneRef = useRef();\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Effetto per gestire le diverse route\n  useEffect(() => {\n    if (certificazioneRef.current && location.pathname) {\n      const path = location.pathname;\n\n      // Mappa le route alle opzioni del componente CertificazioneCavi\n      if (path.includes('/visualizza')) {\n        certificazioneRef.current.handleOptionSelect('visualizzaCertificazioni');\n      } else if (path.includes('/filtra')) {\n        certificazioneRef.current.handleOptionSelect('filtraCertificazioni');\n      } else if (path.includes('/crea')) {\n        certificazioneRef.current.handleOptionSelect('creaCertificazione');\n      } else if (path.includes('/dettagli')) {\n        certificazioneRef.current.handleOptionSelect('dettagliCertificazione');\n      } else if (path.includes('/pdf')) {\n        certificazioneRef.current.handleOptionSelect('generaPdf');\n      } else if (path.includes('/elimina')) {\n        certificazioneRef.current.handleOptionSelect('eliminaCertificazione');\n      } else if (path.includes('/strumenti')) {\n        certificazioneRef.current.handleOptionSelect('gestioneStrumenti');\n      } else {\n        // Route base - mostra visualizza certificazioni\n        certificazioneRef.current.handleOptionSelect('visualizzaCertificazioni');\n      }\n    }\n  }, [location.pathname]);\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Gestisce le notifiche\n  const handleSuccess = message => {\n    // Qui puoi implementare una notifica di successo se necessario\n    console.log('Successo:', message);\n  };\n  const handleError = message => {\n    // Qui puoi implementare una notifica di errore se necessario\n    console.error('Errore:', message);\n  };\n  if (!cantiereId || isNaN(cantiereId)) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: \"Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 22\n        }, this),\n        onClick: handleBackToCantieri,\n        children: \"Torna ai Cantieri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleBackToCantieri,\n          sx: {\n            mr: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          children: \"Certificazione Cavi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => window.location.reload(),\n          sx: {\n            ml: 2\n          },\n          color: \"primary\",\n          title: \"Ricarica la pagina\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CertificazioneCavi, {\n      ref: certificazioneRef,\n      cantiereId: cantiereId,\n      onSuccess: handleSuccess,\n      onError: handleError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n};\n_s(CertificazioneCaviPage, \"qChaAFd9ewnxuqu6/g1V8modph0=\", false, function () {\n  return [useAuth, useNavigate, useLocation];\n});\n_c = CertificazioneCaviPage;\nexport default CertificazioneCaviPage;\nvar _c;\n$RefreshReg$(_c, \"CertificazioneCaviPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Box", "Typography", "Paper", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON>", "ArrowBack", "ArrowBackIcon", "Refresh", "RefreshIcon", "Home", "HomeIcon", "useNavigate", "useLocation", "useAuth", "AdminHomeButton", "CertificazioneCaviImproved", "jsxDEV", "_jsxDEV", "CertificazioneCaviPage", "_s", "isImpersonating", "navigate", "location", "certificazioneRef", "cantiereId", "parseInt", "localStorage", "getItem", "cantiereName", "current", "pathname", "path", "includes", "handleOptionSelect", "handleBackToCantieri", "handleSuccess", "message", "console", "log", "handleError", "error", "isNaN", "children", "severity", "sx", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "startIcon", "onClick", "display", "alignItems", "justifyContent", "mr", "window", "reload", "ml", "color", "title", "CertificazioneCavi", "ref", "onSuccess", "onError", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/CertificazioneCaviPage.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  IconButton,\n  Alert\n} from '@mui/material';\nimport {\n  ArrowBack as ArrowBackIcon,\n  Refresh as RefreshIcon,\n  Home as HomeIcon\n} from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport CertificazioneCaviImproved from '../../components/cavi/CertificazioneCaviImproved';\n\nconst CertificazioneCaviPage = () => {\n  const { isImpersonating } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const certificazioneRef = useRef();\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n  const cantiereName = localStorage.getItem('selectedCantiereName') || `Cantiere ${cantiereId}`;\n\n  // Effetto per gestire le diverse route\n  useEffect(() => {\n    if (certificazioneRef.current && location.pathname) {\n      const path = location.pathname;\n\n      // Mappa le route alle opzioni del componente CertificazioneCavi\n      if (path.includes('/visualizza')) {\n        certificazioneRef.current.handleOptionSelect('visualizzaCertificazioni');\n      } else if (path.includes('/filtra')) {\n        certificazioneRef.current.handleOptionSelect('filtraCertificazioni');\n      } else if (path.includes('/crea')) {\n        certificazioneRef.current.handleOptionSelect('creaCertificazione');\n      } else if (path.includes('/dettagli')) {\n        certificazioneRef.current.handleOptionSelect('dettagliCertificazione');\n      } else if (path.includes('/pdf')) {\n        certificazioneRef.current.handleOptionSelect('generaPdf');\n      } else if (path.includes('/elimina')) {\n        certificazioneRef.current.handleOptionSelect('eliminaCertificazione');\n      } else if (path.includes('/strumenti')) {\n        certificazioneRef.current.handleOptionSelect('gestioneStrumenti');\n      } else {\n        // Route base - mostra visualizza certificazioni\n        certificazioneRef.current.handleOptionSelect('visualizzaCertificazioni');\n      }\n    }\n  }, [location.pathname]);\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n\n\n  // Gestisce le notifiche\n  const handleSuccess = (message) => {\n    // Qui puoi implementare una notifica di successo se necessario\n    console.log('Successo:', message);\n  };\n\n  const handleError = (message) => {\n    // Qui puoi implementare una notifica di errore se necessario\n    console.error('Errore:', message);\n  };\n\n  if (!cantiereId || isNaN(cantiereId)) {\n    return (\n      <Box>\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.\n        </Alert>\n        <Button\n          variant=\"contained\"\n          startIcon={<ArrowBackIcon />}\n          onClick={handleBackToCantieri}\n        >\n          Torna ai Cantieri\n        </Button>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n        <Box sx={{ display: 'flex', alignItems: 'center' }}>\n          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>\n            <ArrowBackIcon />\n          </IconButton>\n          <Typography variant=\"h5\">\n            Certificazione Cavi\n          </Typography>\n          <IconButton\n            onClick={() => window.location.reload()}\n            sx={{ ml: 2 }}\n            color=\"primary\"\n            title=\"Ricarica la pagina\"\n          >\n            <RefreshIcon />\n          </IconButton>\n        </Box>\n        <AdminHomeButton />\n      </Box>\n\n\n\n      <CertificazioneCavi\n        ref={certificazioneRef}\n        cantiereId={cantiereId}\n        onSuccess={handleSuccess}\n        onError={handleError}\n      />\n    </Box>\n  );\n};\n\nexport default CertificazioneCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,KAAK,QACA,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,OAAO,IAAIC,WAAW,EACtBC,IAAI,IAAIC,QAAQ,QACX,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,0BAA0B,MAAM,kDAAkD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1F,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM;IAAEC;EAAgB,CAAC,GAAGP,OAAO,CAAC,CAAC;EACrC,MAAMQ,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,iBAAiB,GAAGzB,MAAM,CAAC,CAAC;;EAElC;EACA,MAAM0B,UAAU,GAAGC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;EAC3E,MAAMC,YAAY,GAAGF,YAAY,CAACC,OAAO,CAAC,sBAAsB,CAAC,IAAI,YAAYH,UAAU,EAAE;;EAE7F;EACA3B,SAAS,CAAC,MAAM;IACd,IAAI0B,iBAAiB,CAACM,OAAO,IAAIP,QAAQ,CAACQ,QAAQ,EAAE;MAClD,MAAMC,IAAI,GAAGT,QAAQ,CAACQ,QAAQ;;MAE9B;MACA,IAAIC,IAAI,CAACC,QAAQ,CAAC,aAAa,CAAC,EAAE;QAChCT,iBAAiB,CAACM,OAAO,CAACI,kBAAkB,CAAC,0BAA0B,CAAC;MAC1E,CAAC,MAAM,IAAIF,IAAI,CAACC,QAAQ,CAAC,SAAS,CAAC,EAAE;QACnCT,iBAAiB,CAACM,OAAO,CAACI,kBAAkB,CAAC,sBAAsB,CAAC;MACtE,CAAC,MAAM,IAAIF,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;QACjCT,iBAAiB,CAACM,OAAO,CAACI,kBAAkB,CAAC,oBAAoB,CAAC;MACpE,CAAC,MAAM,IAAIF,IAAI,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;QACrCT,iBAAiB,CAACM,OAAO,CAACI,kBAAkB,CAAC,wBAAwB,CAAC;MACxE,CAAC,MAAM,IAAIF,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAChCT,iBAAiB,CAACM,OAAO,CAACI,kBAAkB,CAAC,WAAW,CAAC;MAC3D,CAAC,MAAM,IAAIF,IAAI,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;QACpCT,iBAAiB,CAACM,OAAO,CAACI,kBAAkB,CAAC,uBAAuB,CAAC;MACvE,CAAC,MAAM,IAAIF,IAAI,CAACC,QAAQ,CAAC,YAAY,CAAC,EAAE;QACtCT,iBAAiB,CAACM,OAAO,CAACI,kBAAkB,CAAC,mBAAmB,CAAC;MACnE,CAAC,MAAM;QACL;QACAV,iBAAiB,CAACM,OAAO,CAACI,kBAAkB,CAAC,0BAA0B,CAAC;MAC1E;IACF;EACF,CAAC,EAAE,CAACX,QAAQ,CAACQ,QAAQ,CAAC,CAAC;;EAEvB;EACA,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IACjCb,QAAQ,CAAC,qBAAqB,CAAC;EACjC,CAAC;;EAID;EACA,MAAMc,aAAa,GAAIC,OAAO,IAAK;IACjC;IACAC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEF,OAAO,CAAC;EACnC,CAAC;EAED,MAAMG,WAAW,GAAIH,OAAO,IAAK;IAC/B;IACAC,OAAO,CAACG,KAAK,CAAC,SAAS,EAAEJ,OAAO,CAAC;EACnC,CAAC;EAED,IAAI,CAACZ,UAAU,IAAIiB,KAAK,CAACjB,UAAU,CAAC,EAAE;IACpC,oBACEP,OAAA,CAAClB,GAAG;MAAA2C,QAAA,gBACFzB,OAAA,CAACb,KAAK;QAACuC,QAAQ,EAAC,OAAO;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EAAC;MAEvC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRhC,OAAA,CAACf,MAAM;QACLgD,OAAO,EAAC,WAAW;QACnBC,SAAS,eAAElC,OAAA,CAACX,aAAa;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7BG,OAAO,EAAElB,oBAAqB;QAAAQ,QAAA,EAC/B;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEhC,OAAA,CAAClB,GAAG;IAAA2C,QAAA,gBACFzB,OAAA,CAAClB,GAAG;MAAC6C,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEQ,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAgB,CAAE;MAAAb,QAAA,gBACzFzB,OAAA,CAAClB,GAAG;QAAC6C,EAAE,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAZ,QAAA,gBACjDzB,OAAA,CAACd,UAAU;UAACiD,OAAO,EAAElB,oBAAqB;UAACU,EAAE,EAAE;YAAEY,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,eACvDzB,OAAA,CAACX,aAAa;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACbhC,OAAA,CAACjB,UAAU;UAACkD,OAAO,EAAC,IAAI;UAAAR,QAAA,EAAC;QAEzB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbhC,OAAA,CAACd,UAAU;UACTiD,OAAO,EAAEA,CAAA,KAAMK,MAAM,CAACnC,QAAQ,CAACoC,MAAM,CAAC,CAAE;UACxCd,EAAE,EAAE;YAAEe,EAAE,EAAE;UAAE,CAAE;UACdC,KAAK,EAAC,SAAS;UACfC,KAAK,EAAC,oBAAoB;UAAAnB,QAAA,eAE1BzB,OAAA,CAACT,WAAW;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNhC,OAAA,CAACH,eAAe;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAINhC,OAAA,CAAC6C,kBAAkB;MACjBC,GAAG,EAAExC,iBAAkB;MACvBC,UAAU,EAAEA,UAAW;MACvBwC,SAAS,EAAE7B,aAAc;MACzB8B,OAAO,EAAE1B;IAAY;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAxGID,sBAAsB;EAAA,QACEL,OAAO,EAClBF,WAAW,EACXC,WAAW;AAAA;AAAAsD,EAAA,GAHxBhD,sBAAsB;AA0G5B,eAAeA,sBAAsB;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}