from .auth import Token, TokenData, UserLogin, CantiereLogin
from .user import UserBase, UserCreate, UserUpdate, UserInDB
from .cantiere import CantiereBase, CantiereCreate, CantiereUpdate, CantiereInDB
from .parco_cavi import ParcoCavoBase, ParcoCavoCreate, ParcoCavoUpdate, ParcoCavoInDB, StoricoUtilizzoBobina
from .comanda import (
    ComandaBase, ComandaCreate, ComandaUpdate, ComandaInDB,
    ComandaDettaglioBase, ComandaDettaglioCreate, ComandaDettaglioInDB,
    AssegnaCaviRequest, DatiPosaRequest, DatiCollegamentoRequest, CambiaStatoRequest,
    ComandaResponse, ComandaDettagliataResponse, ComandaListResponse, StatisticheComande
)

# Importa altri schemi man mano che vengono creati
