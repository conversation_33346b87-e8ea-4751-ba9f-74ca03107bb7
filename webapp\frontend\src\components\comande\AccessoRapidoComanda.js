import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Alert,
  Paper,
  Chip,
  List,
  ListItem,
  ListItemText,
  Divider,
  Grid
} from '@mui/material';
import {
  Search as SearchIcon,
  Assignment as ComandaIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import comandeService from '../../services/comandeService';

const AccessoRapidoComanda = () => {
  const [codiceComanda, setCodiceComanda] = useState('');
  const [comanda, setComanda] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleCercaComanda = async () => {
    if (!codiceComanda.trim()) {
      setError('Inserisci un codice comanda');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await comandeService.getDettagliComanda(codiceComanda.trim());
      setComanda(response);
    } catch (err) {
      console.error('Errore nella ricerca:', err);
      setError('Comanda non trovata o errore nella ricerca');
      setComanda(null);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleCercaComanda();
    }
  };

  const getTipoComandaLabel = (tipo) => {
    switch (tipo) {
      case 'POSA': return 'Posa';
      case 'COLLEGAMENTO_PARTENZA': return 'Collegamento Partenza';
      case 'COLLEGAMENTO_ARRIVO': return 'Collegamento Arrivo';
      case 'TESTING': return 'Testing';
      default: return tipo;
    }
  };

  const getStatoColor = (stato) => {
    switch (stato) {
      case 'CREATA': return 'default';
      case 'ASSEGNATA': return 'primary';
      case 'IN_CORSO': return 'warning';
      case 'COMPLETATA': return 'success';
      case 'ANNULLATA': return 'error';
      default: return 'default';
    }
  };

  const getPrioritaColor = (priorita) => {
    switch (priorita) {
      case 'BASSA': return 'default';
      case 'NORMALE': return 'primary';
      case 'ALTA': return 'warning';
      case 'URGENTE': return 'error';
      default: return 'default';
    }
  };

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
      {/* Header */}
      <Paper sx={{ p: 3, mb: 3, textAlign: 'center', bgcolor: 'primary.main', color: 'white' }}>
        <ComandaIcon sx={{ fontSize: 48, mb: 1 }} />
        <Typography variant="h4" gutterBottom>
          Accesso Rapido Comanda
        </Typography>
        <Typography variant="body1">
          Simula l'accesso da app mobile per responsabili
        </Typography>
      </Paper>

      {/* Ricerca */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Cerca Comanda per Codice
          </Typography>
          <Box display="flex" gap={2} alignItems="center">
            <TextField
              fullWidth
              label="Codice Comanda"
              value={codiceComanda}
              onChange={(e) => setCodiceComanda(e.target.value.toUpperCase())}
              onKeyPress={handleKeyPress}
              placeholder="es: POS20241201001"
              helperText="Inserisci il codice univoco della comanda"
            />
            <Button
              variant="contained"
              startIcon={<SearchIcon />}
              onClick={handleCercaComanda}
              disabled={loading}
              sx={{ minWidth: 120 }}
            >
              {loading ? 'Cerca...' : 'Cerca'}
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Errore */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Risultato */}
      {comanda && (
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center" gap={2} mb={2}>
              <ComandaIcon color="primary" />
              <Typography variant="h5">
                {comanda.codice_comanda}
              </Typography>
              <Chip 
                label={comanda.stato}
                color={getStatoColor(comanda.stato)}
                size="small"
              />
            </Box>

            <Grid container spacing={3}>
              {/* Informazioni Principali */}
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom color="primary">
                  📋 Informazioni Comanda
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemText 
                      primary="Tipo Attività" 
                      secondary={getTipoComandaLabel(comanda.tipo_comanda)} 
                    />
                  </ListItem>
                  <Divider />
                  <ListItem>
                    <ListItemText 
                      primary="Priorità" 
                      secondary={
                        <Chip 
                          label={comanda.priorita || 'NORMALE'}
                          color={getPrioritaColor(comanda.priorita || 'NORMALE')}
                          size="small"
                        />
                      }
                    />
                  </ListItem>
                  <Divider />
                  <ListItem>
                    <ListItemText 
                      primary="Responsabile" 
                      secondary={
                        <Box display="flex" alignItems="center" gap={1}>
                          <PersonIcon fontSize="small" />
                          {comanda.responsabile}
                        </Box>
                      }
                    />
                  </ListItem>
                  {comanda.data_scadenza && (
                    <>
                      <Divider />
                      <ListItem>
                        <ListItemText 
                          primary="Scadenza" 
                          secondary={
                            <Box display="flex" alignItems="center" gap={1}>
                              <ScheduleIcon fontSize="small" />
                              {new Date(comanda.data_scadenza).toLocaleDateString('it-IT')}
                            </Box>
                          }
                        />
                      </ListItem>
                    </>
                  )}
                </List>
              </Grid>

              {/* Dettagli Lavoro */}
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom color="primary">
                  🔧 Dettagli Lavoro
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemText 
                      primary="Descrizione" 
                      secondary={comanda.descrizione || 'Nessuna descrizione'} 
                    />
                  </ListItem>
                  {comanda.note_capo_cantiere && (
                    <>
                      <Divider />
                      <ListItem>
                        <ListItemText 
                          primary="Istruzioni Capo Cantiere" 
                          secondary={comanda.note_capo_cantiere} 
                        />
                      </ListItem>
                    </>
                  )}
                  <Divider />
                  <ListItem>
                    <ListItemText 
                      primary="Cavi Assegnati" 
                      secondary={`${comanda.numero_cavi_assegnati || 0} cavi`} 
                    />
                  </ListItem>
                  <Divider />
                  <ListItem>
                    <ListItemText 
                      primary="Progresso" 
                      secondary={`${(comanda.percentuale_completamento || 0).toFixed(1)}% completato`} 
                    />
                  </ListItem>
                </List>
              </Grid>
            </Grid>

            {/* Note Future App Mobile */}
            <Alert severity="info" sx={{ mt: 3 }}>
              <Typography variant="body2">
                <strong>🚀 Futuro App Mobile:</strong> Qui il responsabile potrà aggiornare il rapportino di lavoro giornaliero:
                <br />• Segnare cavi posati/collegati/testati
                <br />• Indicare bobine utilizzate
                <br />• Aggiungere note e problemi riscontrati
                <br />• Aggiornare il progresso in tempo reale
              </Typography>
            </Alert>

            {/* Simulazione Rapportino */}
            <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
              <Typography variant="h6" gutterBottom>
                📝 Rapportino Lavoro (Simulazione)
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Questa sezione sarà implementata nell'app mobile per permettere al responsabile 
                di aggiornare il lavoro svolto direttamente dal cantiere.
              </Typography>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Istruzioni */}
      {!comanda && !error && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              💡 Come funziona
            </Typography>
            <Typography variant="body2" paragraph>
              1. Il <strong>capo cantiere</strong> crea le comande dal sistema web
            </Typography>
            <Typography variant="body2" paragraph>
              2. Ogni comanda ha un <strong>codice univoco</strong> (es: POS20241201001)
            </Typography>
            <Typography variant="body2" paragraph>
              3. Il <strong>responsabile</strong> accede alla comanda tramite app mobile usando il codice
            </Typography>
            <Typography variant="body2" paragraph>
              4. Il responsabile usa la comanda come <strong>rapportino di lavoro giornaliero</strong>
            </Typography>
            <Typography variant="body2">
              5. Il sistema traccia tutto il lavoro svolto per reportistica e controllo
            </Typography>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default AccessoRapidoComanda;
