#!/usr/bin/env python3
"""
Test script per verificare il funzionamento del modulo comande e delle API.
"""

import sys
import os
import requests
import json
from datetime import date

# Aggiungi il percorso del progetto
sys.path.append('.')

def test_modulo_comande():
    """Test del modulo comande base"""
    print("🧪 Test del modulo comande...")
    
    try:
        from modules.comande import (
            crea_comanda, ottieni_comande_cantiere, 
            ottieni_dettagli_comanda, elimina_comanda
        )
        
        # Test creazione comanda
        print("  ✅ Import del modulo riuscito")
        
        # Test creazione comanda
        codice_comanda = crea_comanda(
            id_cantiere=1,
            tipo_comanda="POSA",
            descrizione="Test comanda di posa",
            responsabile="Test User"
        )
        
        if codice_comanda:
            print(f"  ✅ Comanda creata con successo: {codice_comanda}")
            
            # Test recupero dettagli
            dettagli = ottieni_dettagli_comanda(codice_comanda)
            if dettagli:
                print(f"  ✅ Dettagli comanda recuperati: {dettagli['tipo_comanda']}")
            else:
                print("  ❌ Errore nel recupero dettagli comanda")
            
            # Test recupero comande cantiere
            comande = ottieni_comande_cantiere(1)
            print(f"  ✅ Trovate {len(comande)} comande per il cantiere 1")
            
            # Cleanup - elimina la comanda di test
            if elimina_comanda(codice_comanda):
                print("  ✅ Comanda di test eliminata")
            else:
                print("  ⚠️ Impossibile eliminare la comanda di test")
        else:
            print("  ❌ Errore nella creazione della comanda")
            
    except Exception as e:
        print(f"  ❌ Errore nel test del modulo: {str(e)}")
        return False
    
    return True

def test_api_comande(base_url="http://localhost:8001"):
    """Test delle API comande"""
    print(f"🌐 Test delle API comande su {base_url}...")
    
    try:
        # Test health check
        response = requests.get(f"{base_url}/api/health", timeout=5)
        if response.status_code == 200:
            print("  ✅ Health check API riuscito")
        else:
            print(f"  ❌ Health check fallito: {response.status_code}")
            return False
        
        # Test endpoint comande (senza autenticazione per ora)
        response = requests.get(f"{base_url}/api/comande/cantiere/1", timeout=5)
        if response.status_code in [200, 401]:  # 401 è OK, significa che l'endpoint esiste ma richiede auth
            print("  ✅ Endpoint comande raggiungibile")
        else:
            print(f"  ❌ Endpoint comande non raggiungibile: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("  ❌ Impossibile connettersi al server API")
        return False
    except Exception as e:
        print(f"  ❌ Errore nel test API: {str(e)}")
        return False
    
    return True

def test_database_connection():
    """Test della connessione al database"""
    print("🗄️ Test connessione database...")
    
    try:
        from modules.database_pg import database_connection
        
        with database_connection(dict_cursor=True) as (conn, c):
            c.execute("SELECT 1")
            result = c.fetchone()
            if result and result[0] == 1:
                print("  ✅ Connessione database PostgreSQL riuscita")

                # Test esistenza tabella Comande
                c.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_name = 'comande'
                    )
                """)
                exists = c.fetchone()[0]
                if exists:
                    print("  ✅ Tabella Comande esistente")
                else:
                    print("  ❌ Tabella Comande non trovata")
                    return False
            else:
                print("  ❌ Errore nella query di test")
                return False
                
    except Exception as e:
        print(f"  ❌ Errore connessione database: {str(e)}")
        return False
    
    return True

def main():
    """Funzione principale di test"""
    print("🚀 Avvio test completo del modulo comande\n")
    
    # Test database
    db_ok = test_database_connection()
    print()
    
    # Test modulo
    modulo_ok = test_modulo_comande()
    print()
    
    # Test API (solo se il database funziona)
    api_ok = False
    if db_ok:
        api_ok = test_api_comande()
    else:
        print("🌐 Test API saltato a causa di problemi database")
    
    print("\n" + "="*50)
    print("📊 RISULTATI TEST:")
    print(f"  Database: {'✅ OK' if db_ok else '❌ ERRORE'}")
    print(f"  Modulo:   {'✅ OK' if modulo_ok else '❌ ERRORE'}")
    print(f"  API:      {'✅ OK' if api_ok else '❌ ERRORE'}")
    
    if db_ok and modulo_ok:
        print("\n🎉 Il modulo comande è pronto per l'uso!")
        if not api_ok:
            print("💡 Suggerimento: Avvia il backend con 'python webapp/run_backend.py'")
    else:
        print("\n⚠️ Ci sono problemi da risolvere prima di procedere.")
    
    return db_ok and modulo_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
