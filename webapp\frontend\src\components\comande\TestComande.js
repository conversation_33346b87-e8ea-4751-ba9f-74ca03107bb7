import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>ton,
  Ty<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  TextField,
  Grid
} from '@mui/material';
import comandeService from '../../services/comandeService';

const TestComande = () => {
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const [cantiereId, setCantiereId] = useState('1');

  const testCreateComanda = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const comandaData = {
        id_cantiere: parseInt(cantiereId),
        tipo_comanda: 'POSA',
        descrizione: 'Test comanda creata dal frontend',
        responsabile: 'Test User'
      };
      
      const response = await comandeService.createComanda(comandaData);
      setResult(response);
    } catch (err) {
      setError(err.message || 'Errore nella creazione della comanda');
    } finally {
      setLoading(false);
    }
  };

  const testGetComande = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await comandeService.getComande(cantiereId);
      setResult(response);
    } catch (err) {
      setError(err.message || 'Errore nel recupero delle comande');
    } finally {
      setLoading(false);
    }
  };

  const testGetStatistiche = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await comandeService.getStatisticheComande(cantiereId);
      setResult(response);
    } catch (err) {
      setError(err.message || 'Errore nel recupero delle statistiche');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Test Modulo Comande
      </Typography>
      
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Configurazione Test
          </Typography>
          <TextField
            label="ID Cantiere"
            value={cantiereId}
            onChange={(e) => setCantiereId(e.target.value)}
            type="number"
            sx={{ mb: 2 }}
          />
        </CardContent>
      </Card>

      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={4}>
          <Button
            variant="contained"
            onClick={testCreateComanda}
            disabled={loading}
            fullWidth
          >
            Test Crea Comanda
          </Button>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Button
            variant="contained"
            onClick={testGetComande}
            disabled={loading}
            fullWidth
          >
            Test Get Comande
          </Button>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Button
            variant="contained"
            onClick={testGetStatistiche}
            disabled={loading}
            fullWidth
          >
            Test Statistiche
          </Button>
        </Grid>
      </Grid>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {result && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Risultato
            </Typography>
            <pre style={{ 
              backgroundColor: '#f5f5f5', 
              padding: '16px', 
              borderRadius: '4px',
              overflow: 'auto',
              fontSize: '12px'
            }}>
              {JSON.stringify(result, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default TestComande;
