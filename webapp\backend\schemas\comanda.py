from datetime import date, datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class ComandaBase(BaseModel):
    """Schema base per le comande."""
    tipo_comanda: str = Field(..., description="Tipo di comanda: POSA, COLLEGAMENTO_PARTENZA, COLLEGAMENTO_ARRIVO, TESTING")
    descrizione: Optional[str] = Field(None, description="Descrizione della comanda")
    data_scadenza: Optional[date] = Field(None, description="Data di scadenza della comanda")
    responsabile: str = Field(..., description="Responsabile che eseguirà il lavoro (obbligatorio)")
    priorita: Optional[str] = Field("NORMALE", description="Priorità: BASSA, NORMALE, ALTA, URGENTE")
    note_capo_cantiere: Optional[str] = Field(None, description="Note del capo cantiere per il responsabile")


class ComandaCreate(ComandaBase):
    """Schema per la creazione di una nuova comanda."""
    id_cantiere: int = Field(..., description="ID del cantiere")


class ComandaUpdate(BaseModel):
    """Schema per l'aggiornamento di una comanda."""
    descrizione: Optional[str] = None
    data_scadenza: Optional[date] = None
    responsabile: Optional[str] = None
    stato: Optional[str] = None


class ComandaInDB(ComandaBase):
    """Schema per una comanda nel database."""
    codice_comanda: str
    data_creazione: date
    data_completamento: Optional[date] = None
    stato: str
    id_cantiere: int

    class Config:
        orm_mode = True


class ComandaDettaglioBase(BaseModel):
    """Schema base per i dettagli delle comande."""
    id_cavo: str = Field(..., description="ID del cavo")
    id_cantiere: int = Field(..., description="ID del cantiere")


class ComandaDettaglioCreate(ComandaDettaglioBase):
    """Schema per la creazione di un dettaglio comanda."""
    codice_comanda: str = Field(..., description="Codice della comanda")


class ComandaDettaglioInDB(ComandaDettaglioBase):
    """Schema per un dettaglio comanda nel database."""
    id_dettaglio: int
    codice_comanda: str

    class Config:
        orm_mode = True


class AssegnaCaviRequest(BaseModel):
    """Schema per la richiesta di assegnazione cavi a una comanda."""
    lista_id_cavi: List[str] = Field(..., description="Lista degli ID dei cavi da assegnare")


class DatiPosaRequest(BaseModel):
    """Schema per l'aggiornamento dei dati di posa."""
    dati_posa: Dict[str, Dict[str, Any]] = Field(..., description="Dati di posa per ogni cavo")


class DatiCollegamentoRequest(BaseModel):
    """Schema per l'aggiornamento dei dati di collegamento."""
    dati_collegamento: Dict[str, Dict[str, Any]] = Field(..., description="Dati di collegamento per ogni cavo")


class CambiaStatoRequest(BaseModel):
    """Schema per il cambio di stato di una comanda."""
    nuovo_stato: str = Field(..., description="Nuovo stato della comanda")


class ComandaResponse(ComandaInDB):
    """Schema di risposta per una comanda con informazioni aggiuntive."""
    numero_cavi_assegnati: Optional[int] = 0
    cavi_completati: Optional[int] = 0
    percentuale_completamento: Optional[float] = 0.0


class ComandaDettagliataResponse(ComandaResponse):
    """Schema di risposta per una comanda con tutti i dettagli."""
    cavi: List[Dict[str, Any]] = []
    dettagli: List[ComandaDettaglioInDB] = []


class ComandaListResponse(BaseModel):
    """Schema di risposta per la lista delle comande."""
    comande: List[ComandaResponse]
    totale: int
    pagina: int
    per_pagina: int


class StatisticheComande(BaseModel):
    """Schema per le statistiche delle comande."""
    totale_comande: int = 0
    comande_create: int = 0
    comande_assegnate: int = 0
    comande_in_corso: int = 0
    comande_completate: int = 0
    comande_annullate: int = 0
    percentuale_completamento_medio: float = 0.0


# Schemi per il rapportino di lavoro giornaliero (futuro app mobile)
class RapportinoLavoroRequest(BaseModel):
    """Schema per l'aggiornamento del rapportino di lavoro."""
    codice_comanda: str = Field(..., description="Codice della comanda")
    id_cavo: str = Field(..., description="ID del cavo lavorato")
    attivita_svolta: str = Field(..., description="Attività svolta: POSATO, COLLEGATO, TESTATO")
    bobina_utilizzata: Optional[str] = Field(None, description="Codice bobina utilizzata (per posa)")
    metri_posati: Optional[float] = Field(None, description="Metri di cavo posati")
    note_lavoro: Optional[str] = Field(None, description="Note del responsabile sul lavoro svolto")
    problemi_riscontrati: Optional[str] = Field(None, description="Eventuali problemi riscontrati")
    data_lavoro: Optional[date] = Field(None, description="Data del lavoro (default oggi)")


class RapportinoLavoroResponse(BaseModel):
    """Schema di risposta per il rapportino di lavoro."""
    id_rapportino: int
    codice_comanda: str
    id_cavo: str
    attivita_svolta: str
    bobina_utilizzata: Optional[str] = None
    metri_posati: Optional[float] = None
    note_lavoro: Optional[str] = None
    problemi_riscontrati: Optional[str] = None
    data_lavoro: date
    responsabile: str

    class Config:
        orm_mode = True


class ComandaMobileResponse(BaseModel):
    """Schema di risposta per l'app mobile - vista comanda per responsabile."""
    codice_comanda: str
    tipo_comanda: str
    descrizione: Optional[str] = None
    responsabile: str
    priorita: str
    note_capo_cantiere: Optional[str] = None
    data_scadenza: Optional[date] = None
    stato: str
    cavi_assegnati: List[Dict[str, Any]] = []
    rapportini_esistenti: List[RapportinoLavoroResponse] = []
    progresso_completamento: float = 0.0
