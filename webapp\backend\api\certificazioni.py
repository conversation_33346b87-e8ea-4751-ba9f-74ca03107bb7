from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from typing import List, Any, Optional
from datetime import datetime, date
import os
import tempfile
from pathlib import Path

from backend.database import get_db
from backend.models.user import User
from backend.models.cantiere import Cantiere
from backend.models.cavo import Cavo
from backend.models.certificazione_cavo import CertificazioneCavo
from backend.models.strumento_certificato import StrumentoCertificato
from backend.schemas.certificazione_cavo import (
    CertificazioneCavoCreate,
    CertificazioneCavoUpdate,
    CertificazioneCavoResponse,
    CertificazioneCavoListResponse
)
from backend.core.security import get_current_active_user

# Importa ReportLab per la generazione PDF
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib import colors
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

router = APIRouter()


def genera_numero_certificato(db: Session, id_cantiere: int) -> str:
    """
    Genera il prossimo numero di certificato per il cantiere.
    Formato: CERT0001, CERT0002, etc.
    """
    # Trova l'ultimo numero di certificato per il cantiere
    ultimo_certificato = db.query(CertificazioneCavo).filter(
        CertificazioneCavo.id_cantiere == id_cantiere,
        CertificazioneCavo.numero_certificato.like('CERT%')
    ).order_by(CertificazioneCavo.numero_certificato.desc()).first()

    if ultimo_certificato:
        try:
            # Estrae il numero dalla stringa CERT0001 -> 1
            ultimo_numero = int(ultimo_certificato.numero_certificato[4:])
            nuovo_numero = ultimo_numero + 1
        except (ValueError, IndexError):
            nuovo_numero = 1
    else:
        nuovo_numero = 1

    return f"CERT{nuovo_numero:04d}"


@router.get("/{cantiere_id}/certificazioni", response_model=List[CertificazioneCavoListResponse])
def get_certificazioni_cantiere(
    cantiere_id: int,
    filtro_cavo: Optional[str] = Query(None, description="Filtro per ID cavo"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Recupera tutte le certificazioni di un cantiere con possibilità di filtrare per ID cavo.
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Query base con join per ottenere informazioni del cavo
    query = db.query(
        CertificazioneCavo.id_certificazione,
        CertificazioneCavo.id_cavo,
        CertificazioneCavo.numero_certificato,
        CertificazioneCavo.data_certificazione,
        CertificazioneCavo.id_operatore,
        CertificazioneCavo.valore_isolamento,
        CertificazioneCavo.strumento_utilizzato,
        CertificazioneCavo.lunghezza_misurata,
        Cavo.tipologia.label('cavo_tipologia'),
        Cavo.sezione.label('cavo_sezione')
    ).join(
        Cavo, and_(
            CertificazioneCavo.id_cavo == Cavo.id_cavo,
            CertificazioneCavo.id_cantiere == Cavo.id_cantiere
        )
    ).filter(CertificazioneCavo.id_cantiere == cantiere_id)

    # Applica filtro se specificato
    if filtro_cavo:
        query = query.filter(CertificazioneCavo.id_cavo.like(f"%{filtro_cavo}%"))

    # Ordina per data di certificazione decrescente
    certificazioni = query.order_by(CertificazioneCavo.data_certificazione.desc()).all()

    # Converte i risultati nel formato richiesto
    result = []
    for cert in certificazioni:
        result.append(CertificazioneCavoListResponse(
            id_certificazione=cert.id_certificazione,
            id_cavo=cert.id_cavo,
            numero_certificato=cert.numero_certificato,
            data_certificazione=cert.data_certificazione,
            id_operatore=cert.id_operatore,
            valore_isolamento=cert.valore_isolamento,
            strumento_utilizzato=cert.strumento_utilizzato,
            lunghezza_misurata=cert.lunghezza_misurata,
            cavo_tipologia=cert.cavo_tipologia,
            cavo_sezione=cert.cavo_sezione
        ))

    return result


@router.post("/{cantiere_id}/certificazioni", response_model=CertificazioneCavoResponse)
def create_certificazione(
    cantiere_id: int,
    certificazione_in: CertificazioneCavoCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Crea una nuova certificazione per un cavo.
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che il cavo esista e appartenga al cantiere
    cavo = db.query(Cavo).filter(
        Cavo.id_cavo == certificazione_in.id_cavo,
        Cavo.id_cantiere == cantiere_id
    ).first()

    if not cavo:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cavo {certificazione_in.id_cavo} non trovato nel cantiere {cantiere_id}"
        )

    # Verifica che non esista già una certificazione per questo cavo
    existing_cert = db.query(CertificazioneCavo).filter(
        CertificazioneCavo.id_cantiere == cantiere_id,
        CertificazioneCavo.id_cavo == certificazione_in.id_cavo
    ).first()

    if existing_cert:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Esiste già una certificazione per il cavo {certificazione_in.id_cavo}"
        )

    # Verifica che lo strumento esista se specificato
    if certificazione_in.id_strumento:
        strumento = db.query(StrumentoCertificato).filter(
            StrumentoCertificato.id_strumento == certificazione_in.id_strumento,
            StrumentoCertificato.id_cantiere == cantiere_id
        ).first()

        if not strumento:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Strumento con ID {certificazione_in.id_strumento} non trovato nel cantiere"
            )

    # Genera il numero di certificato
    numero_certificato = genera_numero_certificato(db, cantiere_id)

    # Usa la metratura reale del cavo come lunghezza misurata se non specificata
    lunghezza_misurata = certificazione_in.lunghezza_misurata
    if lunghezza_misurata is None:
        lunghezza_misurata = cavo.metratura_reale

    # Crea la certificazione
    certificazione = CertificazioneCavo(
        id_cantiere=cantiere_id,
        id_cavo=certificazione_in.id_cavo,
        numero_certificato=numero_certificato,
        data_certificazione=date.today(),
        id_operatore=certificazione_in.id_operatore,
        strumento_utilizzato=certificazione_in.strumento_utilizzato,
        id_strumento=certificazione_in.id_strumento,
        lunghezza_misurata=lunghezza_misurata,
        valore_continuita=certificazione_in.valore_continuita or "OK",
        valore_isolamento=certificazione_in.valore_isolamento or "500",
        valore_resistenza=certificazione_in.valore_resistenza or "OK",
        note=certificazione_in.note,
        timestamp_modifica=datetime.now()
    )

    db.add(certificazione)
    db.commit()
    db.refresh(certificazione)

    return certificazione


@router.get("/{cantiere_id}/certificazioni/{certificazione_id}", response_model=CertificazioneCavoResponse)
def get_certificazione(
    cantiere_id: int,
    certificazione_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Recupera i dettagli di una certificazione specifica.
    """
    # Query con join per ottenere tutte le informazioni
    query = db.query(
        CertificazioneCavo,
        Cavo.tipologia.label('cavo_tipologia'),
        Cavo.sezione.label('cavo_sezione'),
        Cavo.ubicazione_partenza.label('cavo_ubicazione_partenza'),
        Cavo.ubicazione_arrivo.label('cavo_ubicazione_arrivo'),
        Cavo.metri_teorici.label('cavo_metri_teorici'),
        Cavo.stato_installazione.label('cavo_stato_installazione'),
        StrumentoCertificato.nome.label('strumento_nome'),
        StrumentoCertificato.marca.label('strumento_marca'),
        StrumentoCertificato.modello.label('strumento_modello')
    ).join(
        Cavo, and_(
            CertificazioneCavo.id_cavo == Cavo.id_cavo,
            CertificazioneCavo.id_cantiere == Cavo.id_cantiere
        )
    ).outerjoin(
        StrumentoCertificato,
        CertificazioneCavo.id_strumento == StrumentoCertificato.id_strumento
    ).filter(
        CertificazioneCavo.id_certificazione == certificazione_id,
        CertificazioneCavo.id_cantiere == cantiere_id
    )

    result = query.first()
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Certificazione con ID {certificazione_id} non trovata nel cantiere {cantiere_id}"
        )

    certificazione = result[0]

    # Crea la risposta con tutte le informazioni
    response = CertificazioneCavoResponse(
        id_certificazione=certificazione.id_certificazione,
        id_cantiere=certificazione.id_cantiere,
        id_cavo=certificazione.id_cavo,
        numero_certificato=certificazione.numero_certificato,
        data_certificazione=certificazione.data_certificazione,
        id_operatore=certificazione.id_operatore,
        strumento_utilizzato=certificazione.strumento_utilizzato,
        id_strumento=certificazione.id_strumento,
        lunghezza_misurata=certificazione.lunghezza_misurata,
        valore_continuita=certificazione.valore_continuita,
        valore_isolamento=certificazione.valore_isolamento,
        valore_resistenza=certificazione.valore_resistenza,
        percorso_certificato=certificazione.percorso_certificato,
        percorso_foto=certificazione.percorso_foto,
        note=certificazione.note,
        timestamp_creazione=certificazione.timestamp_creazione,
        timestamp_modifica=certificazione.timestamp_modifica,
        cavo_tipologia=result.cavo_tipologia,
        cavo_sezione=result.cavo_sezione,
        cavo_ubicazione_partenza=result.cavo_ubicazione_partenza,
        cavo_ubicazione_arrivo=result.cavo_ubicazione_arrivo,
        cavo_metri_teorici=result.cavo_metri_teorici,
        cavo_stato_installazione=result.cavo_stato_installazione,
        strumento_nome=result.strumento_nome,
        strumento_marca=result.strumento_marca,
        strumento_modello=result.strumento_modello
    )

    return response


@router.put("/{cantiere_id}/certificazioni/{certificazione_id}", response_model=CertificazioneCavoResponse)
def update_certificazione(
    cantiere_id: int,
    certificazione_id: int,
    certificazione_update: CertificazioneCavoUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Aggiorna una certificazione esistente.
    """
    certificazione = db.query(CertificazioneCavo).filter(
        CertificazioneCavo.id_certificazione == certificazione_id,
        CertificazioneCavo.id_cantiere == cantiere_id
    ).first()

    if not certificazione:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Certificazione con ID {certificazione_id} non trovata nel cantiere {cantiere_id}"
        )

    # Verifica che lo strumento esista se viene modificato
    if certificazione_update.id_strumento:
        strumento = db.query(StrumentoCertificato).filter(
            StrumentoCertificato.id_strumento == certificazione_update.id_strumento,
            StrumentoCertificato.id_cantiere == cantiere_id
        ).first()

        if not strumento:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Strumento con ID {certificazione_update.id_strumento} non trovato nel cantiere"
            )

    # Aggiorna i campi
    update_data = certificazione_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(certificazione, field, value)

    certificazione.timestamp_modifica = datetime.now()

    db.commit()
    db.refresh(certificazione)

    return certificazione


@router.delete("/{cantiere_id}/certificazioni/{certificazione_id}")
def delete_certificazione(
    cantiere_id: int,
    certificazione_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Elimina una certificazione.
    """
    certificazione = db.query(CertificazioneCavo).filter(
        CertificazioneCavo.id_certificazione == certificazione_id,
        CertificazioneCavo.id_cantiere == cantiere_id
    ).first()

    if not certificazione:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Certificazione con ID {certificazione_id} non trovata nel cantiere {cantiere_id}"
        )

    # Elimina eventuali file associati (PDF, foto)
    # TODO: Implementare eliminazione file fisici se necessario

    db.delete(certificazione)
    db.commit()

    return {"message": "Certificazione eliminata con successo"}


def generate_html_certificazione(
    cantiere_id: int,
    certificazione_id: int,
    db: Session
) -> Optional[str]:
    """
    Genera un HTML per la certificazione che può essere convertito in PDF dal browser.

    Args:
        cantiere_id: ID del cantiere
        certificazione_id: ID della certificazione
        db: Sessione database

    Returns:
        Percorso del file HTML generato o None se errore
    """

    try:
        # Query completa per ottenere tutti i dati necessari
        query = db.query(
            CertificazioneCavo,
            Cantiere.nome.label('cantiere_nome'),
            Cantiere.descrizione.label('cantiere_descrizione'),
            Cavo.tipologia.label('cavo_tipologia'),
            Cavo.sezione.label('cavo_sezione'),
            Cavo.ubicazione_partenza.label('cavo_ubicazione_partenza'),
            Cavo.ubicazione_arrivo.label('cavo_ubicazione_arrivo'),
            Cavo.metri_teorici.label('cavo_metri_teorici'),
            StrumentoCertificato.nome.label('strumento_nome'),
            StrumentoCertificato.marca.label('strumento_marca'),
            StrumentoCertificato.modello.label('strumento_modello'),
            StrumentoCertificato.numero_serie.label('strumento_numero_serie'),
            StrumentoCertificato.data_calibrazione.label('strumento_data_calibrazione'),
            StrumentoCertificato.data_scadenza_calibrazione.label('strumento_data_scadenza')
        ).join(
            Cantiere, CertificazioneCavo.id_cantiere == Cantiere.id_cantiere
        ).join(
            Cavo, and_(
                CertificazioneCavo.id_cavo == Cavo.id_cavo,
                CertificazioneCavo.id_cantiere == Cavo.id_cantiere
            )
        ).outerjoin(
            StrumentoCertificato, and_(
                CertificazioneCavo.id_strumento == StrumentoCertificato.id_strumento,
                CertificazioneCavo.id_cantiere == StrumentoCertificato.id_cantiere
            )
        ).filter(
            CertificazioneCavo.id_certificazione == certificazione_id,
            CertificazioneCavo.id_cantiere == cantiere_id
        ).first()

        if not query:
            return None

        cert = query[0]  # CertificazioneCavo object

        # Crea directory per i certificati se non esiste
        cert_dir = Path("static/certificati") / str(cantiere_id)
        cert_dir.mkdir(parents=True, exist_ok=True)

        # Nome file HTML
        html_filename = f"certificato_{cert.numero_certificato}_{cert.id_cavo}.html"
        html_path = cert_dir / html_filename

        # Crea il contenuto HTML
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Certificato {cert.numero_certificato}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ text-align: center; color: #2c3e50; margin-bottom: 30px; }}
        .section {{ margin-bottom: 20px; }}
        .section h3 {{ color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px; }}
        table {{ width: 100%; border-collapse: collapse; margin-bottom: 15px; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .test-ok {{ color: green; font-weight: bold; }}
        .test-warning {{ color: orange; font-weight: bold; }}
        .test-error {{ color: red; font-weight: bold; }}
        .signature {{ margin-top: 50px; }}
        @media print {{ body {{ margin: 0; }} }}
    </style>
</head>
<body>
    <div class="header">
        <h1>CERTIFICATO DI PROVA CAVO ELETTRICO</h1>
        <h2>N° {cert.numero_certificato}</h2>
    </div>

    <div class="section">
        <h3>INFORMAZIONI CANTIERE</h3>
        <table>
            <tr><th>Cantiere</th><td>{query.cantiere_nome or "N/A"}</td></tr>
            <tr><th>Descrizione</th><td>{query.cantiere_descrizione or "N/A"}</td></tr>
            <tr><th>Data Certificazione</th><td>{cert.data_certificazione.strftime("%d/%m/%Y")}</td></tr>
        </table>
    </div>

    <div class="section">
        <h3>INFORMAZIONI CAVO</h3>
        <table>
            <tr><th>ID Cavo</th><td>{cert.id_cavo}</td></tr>
            <tr><th>Tipologia</th><td>{query.cavo_tipologia or "N/A"}</td></tr>
            <tr><th>Sezione</th><td>{f"{query.cavo_sezione} mm²" if query.cavo_sezione else "N/A"}</td></tr>
            <tr><th>Ubicazione Partenza</th><td>{query.cavo_ubicazione_partenza or "N/A"}</td></tr>
            <tr><th>Ubicazione Arrivo</th><td>{query.cavo_ubicazione_arrivo or "N/A"}</td></tr>
            <tr><th>Metri Teorici</th><td>{f"{query.cavo_metri_teorici} m" if query.cavo_metri_teorici else "N/A"}</td></tr>
            <tr><th>Lunghezza Misurata</th><td>{f"{cert.lunghezza_misurata} m" if cert.lunghezza_misurata else "N/A"}</td></tr>
        </table>
    </div>

    <div class="section">
        <h3>RISULTATI TEST</h3>
        <table>
            <tr><th>Test</th><th>Valore</th><th>Esito</th></tr>
            <tr>
                <td>Continuità</td>
                <td>{cert.valore_continuita or "N/A"}</td>
                <td class="{'test-ok' if cert.valore_continuita == 'OK' else 'test-error'}">
                    {"CONFORME" if cert.valore_continuita == 'OK' else "NON CONFORME"}
                </td>
            </tr>
            <tr>
                <td>Isolamento</td>
                <td>{f"{cert.valore_isolamento} MΩ" if cert.valore_isolamento else "N/A"}</td>
                <td class="{'test-ok' if float(cert.valore_isolamento or 0) >= 500 else 'test-warning'}">
                    {"CONFORME" if float(cert.valore_isolamento or 0) >= 500 else "ATTENZIONE"}
                </td>
            </tr>
            <tr>
                <td>Resistenza</td>
                <td>{cert.valore_resistenza or "N/A"}</td>
                <td class="{'test-ok' if cert.valore_resistenza == 'OK' else 'test-error'}">
                    {"CONFORME" if cert.valore_resistenza == 'OK' else "NON CONFORME"}
                </td>
            </tr>
        </table>
    </div>"""

        # Aggiungi informazioni strumento se disponibili
        if query.strumento_nome:
            html_content += f"""
    <div class="section">
        <h3>STRUMENTO DI MISURA</h3>
        <table>
            <tr><th>Nome</th><td>{query.strumento_nome}</td></tr>
            <tr><th>Marca</th><td>{query.strumento_marca or "N/A"}</td></tr>
            <tr><th>Modello</th><td>{query.strumento_modello or "N/A"}</td></tr>
            <tr><th>Numero Serie</th><td>{query.strumento_numero_serie or "N/A"}</td></tr>
            <tr><th>Data Calibrazione</th><td>{query.strumento_data_calibrazione.strftime("%d/%m/%Y") if query.strumento_data_calibrazione else "N/A"}</td></tr>
            <tr><th>Scadenza Calibrazione</th><td>{query.strumento_data_scadenza.strftime("%d/%m/%Y") if query.strumento_data_scadenza else "N/A"}</td></tr>
        </table>
    </div>"""

        # Aggiungi note se presenti
        if cert.note:
            html_content += f"""
    <div class="section">
        <h3>NOTE</h3>
        <p>{cert.note}</p>
    </div>"""

        # Chiudi HTML
        html_content += f"""
    <div class="signature">
        <h3>OPERATORE CERTIFICATORE</h3>
        <br><br>
        <p>_________________________________________________</p>
        <p><strong>Operatore:</strong> {cert.id_operatore}</p>
        <p><strong>Certificato N°:</strong> {cert.numero_certificato}</p>
        <p><strong>Data:</strong> {cert.data_certificazione.strftime("%d/%m/%Y")}</p>
    </div>
</body>
</html>"""

        # Salva il file HTML
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        # Aggiorna il percorso nel database
        cert.percorso_certificato = str(html_path)
        db.commit()

        return str(html_path)

    except Exception as e:
        print(f"Errore nella generazione HTML: {e}")
        return None


@router.get("/{cantiere_id}/certificazioni/{certificazione_id}/pdf")
def generate_certificazione_pdf(
    cantiere_id: int,
    certificazione_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Genera e restituisce il certificato HTML (convertibile in PDF dal browser).
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che la certificazione esista
    certificazione = db.query(CertificazioneCavo).filter(
        CertificazioneCavo.id_certificazione == certificazione_id,
        CertificazioneCavo.id_cantiere == cantiere_id
    ).first()

    if not certificazione:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Certificazione con ID {certificazione_id} non trovata nel cantiere {cantiere_id}"
        )

    # Genera il certificato HTML
    html_path = generate_html_certificazione(cantiere_id, certificazione_id, db)

    if not html_path:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Errore durante la generazione del certificato"
        )

    # Restituisci URL per il download
    filename = os.path.basename(html_path)
    file_url = f"/static/certificati/{cantiere_id}/{filename}"

    return {
        "success": True,
        "file_url": file_url,
        "filename": filename,
        "certificazione_id": certificazione_id
    }
