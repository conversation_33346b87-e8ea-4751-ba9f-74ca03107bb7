{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\ComandeList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Button, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem, Alert, CircularProgress, Tooltip, Grid, List, ListItem, ListItemText, Divider } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Visibility as ViewIcon, Assignment as AssignIcon, Refresh as RefreshIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ComandeList = ({\n  cantiereId,\n  cantiereName\n}) => {\n  _s();\n  const [comande, setComande] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [dialogMode, setDialogMode] = useState('create'); // 'create', 'edit', 'view'\n  const [formData, setFormData] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: ''\n  });\n  const [statistiche, setStatistiche] = useState(null);\n  const [caviAssegnazione, setCaviAssegnazione] = useState('');\n\n  // Carica le comande al mount del componente\n  useEffect(() => {\n    if (cantiereId) {\n      loadComande();\n      loadStatistiche();\n    }\n  }, [cantiereId]);\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      const response = await comandeService.getComande(cantiereId);\n      setComande(response.comande || []);\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n  const handleOpenDialog = (mode, comanda = null) => {\n    setDialogMode(mode);\n    setSelectedComanda(comanda);\n    if (mode === 'create') {\n      setFormData({\n        tipo_comanda: 'POSA',\n        descrizione: '',\n        responsabile: '',\n        data_scadenza: ''\n      });\n    } else if (mode === 'edit' && comanda) {\n      setFormData({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || ''\n      });\n    }\n    setOpenDialog(true);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedComanda(null);\n    setCaviAssegnazione('');\n    setFormData({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: ''\n    });\n  };\n  const handleSubmit = async () => {\n    try {\n      if (dialogMode === 'create') {\n        await comandeService.createComanda({\n          ...formData,\n          id_cantiere: parseInt(cantiereId)\n        });\n      } else if (dialogMode === 'edit') {\n        // Implementare update quando sarà disponibile l'endpoint\n        console.log('Update non ancora implementato');\n      } else if (dialogMode === 'assign') {\n        // Assegnazione cavi\n        if (!caviAssegnazione.trim()) {\n          setError('Inserisci almeno un ID cavo');\n          return;\n        }\n        const listaIdCavi = caviAssegnazione.split(',').map(id => id.trim()).filter(id => id);\n        await comandeService.assegnaCavi(selectedComanda.codice_comanda, listaIdCavi);\n      }\n      handleCloseDialog();\n      loadComande();\n      loadStatistiche();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n  const handleDelete = async codiceComanda => {\n    if (window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      try {\n        await comandeService.deleteComanda(codiceComanda);\n        loadComande();\n        loadStatistiche();\n      } catch (err) {\n        console.error('Errore nell\\'eliminazione:', err);\n        setError('Errore nell\\'eliminazione della comanda');\n      }\n    }\n  };\n  const getStatoColor = stato => {\n    switch (stato) {\n      case 'CREATA':\n        return 'default';\n      case 'ASSEGNATA':\n        return 'primary';\n      case 'IN_CORSO':\n        return 'warning';\n      case 'COMPLETATA':\n        return 'success';\n      case 'ANNULLATA':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    switch (tipo) {\n      case 'POSA':\n        return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA':\n        return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO':\n        return 'Collegamento Arrivo';\n      default:\n        return tipo;\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: statistiche && /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        mb: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Totale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.totale_comande\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Create\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_create\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Assegnate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_assegnate\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"In Corso\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_in_corso\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"Completate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: statistiche.comande_completate\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 2,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                color: \"textSecondary\",\n                gutterBottom: true,\n                children: \"% Completamento\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h5\",\n                children: [statistiche.percentuale_completamento_medio.toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 2,\n      flexWrap: \"wrap\",\n      gap: 1,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        gap: 1,\n        flexWrap: \"wrap\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 24\n          }, this),\n          onClick: () => handleOpenDialog('create'),\n          children: \"Nuova Comanda\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(AssignIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 24\n          }, this),\n          onClick: () => {\n            if (comande.length === 0) {\n              setError('Nessuna comanda disponibile per l\\'assegnazione');\n              return;\n            }\n            // Apri dialog per selezionare comanda\n            setError('Seleziona una comanda dalla tabella e clicca sull\\'icona \"Assegna Cavi\"');\n          },\n          disabled: comande.length === 0,\n          children: \"Assegna Cavi\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 22\n        }, this),\n        onClick: () => {\n          loadComande();\n          loadStatistiche();\n        },\n        children: \"Aggiorna\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Codice\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Tipo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Descrizione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Data Creazione\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Stato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Cavi Assegnati\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Completamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Azioni\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: comande.map(comanda => /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                fontWeight: \"bold\",\n                children: comanda.codice_comanda\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: getTipoComandaLabel(comanda.tipo_comanda),\n                size: \"small\",\n                variant: \"outlined\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.descrizione\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.responsabile\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: new Date(comanda.data_creazione).toLocaleDateString('it-IT')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: comanda.stato,\n                color: getStatoColor(comanda.stato),\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.numero_cavi_assegnati || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: comanda.percentuale_completamento ? `${comanda.percentuale_completamento.toFixed(1)}%` : '0%'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Visualizza\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleOpenDialog('view', comanda),\n                  children: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Modifica\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleOpenDialog('edit', comanda),\n                  children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Assegna Cavi\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleOpenDialog('assign', comanda),\n                  color: \"primary\",\n                  children: /*#__PURE__*/_jsxDEV(AssignIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Elimina\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  size: \"small\",\n                  onClick: () => handleDelete(comanda.codice_comanda),\n                  color: \"error\",\n                  children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this)]\n          }, comanda.codice_comanda, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this), comande.length === 0 && !loading && /*#__PURE__*/_jsxDEV(Box, {\n      textAlign: \"center\",\n      py: 4,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"textSecondary\",\n        children: \"Nessuna comanda trovata\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"textSecondary\",\n        children: \"Clicca su \\\"Nuova Comanda\\\" per iniziare\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: handleCloseDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [dialogMode === 'create' && 'Nuova Comanda', dialogMode === 'edit' && 'Modifica Comanda', dialogMode === 'view' && 'Dettagli Comanda', dialogMode === 'assign' && `Assegna Cavi - ${selectedComanda === null || selectedComanda === void 0 ? void 0 : selectedComanda.codice_comanda}`]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 1\n          },\n          children: dialogMode === 'assign' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              sx: {\n                mb: 2\n              },\n              children: \"Inserisci gli ID dei cavi da assegnare alla comanda, separati da virgola.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"ID Cavi (separati da virgola)\",\n              value: caviAssegnazione,\n              onChange: e => setCaviAssegnazione(e.target.value),\n              margin: \"normal\",\n              placeholder: \"es: CAVO001, CAVO002, CAVO003\",\n              helperText: \"Esempio: CAVO001, CAVO002, CAVO003\",\n              multiline: true,\n              rows: 3\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Tipo Comanda\",\n              value: formData.tipo_comanda,\n              onChange: e => setFormData({\n                ...formData,\n                tipo_comanda: e.target.value\n              }),\n              margin: \"normal\",\n              disabled: dialogMode === 'view',\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"POSA\",\n                children: \"Posa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_PARTENZA\",\n                children: \"Collegamento Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_ARRIVO\",\n                children: \"Collegamento Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Descrizione\",\n              value: formData.descrizione,\n              onChange: e => setFormData({\n                ...formData,\n                descrizione: e.target.value\n              }),\n              margin: \"normal\",\n              multiline: true,\n              rows: 3,\n              disabled: dialogMode === 'view'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Responsabile\",\n              value: formData.responsabile,\n              onChange: e => setFormData({\n                ...formData,\n                responsabile: e.target.value\n              }),\n              margin: \"normal\",\n              disabled: dialogMode === 'view'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Data Scadenza\",\n              type: \"date\",\n              value: formData.data_scadenza,\n              onChange: e => setFormData({\n                ...formData,\n                data_scadenza: e.target.value\n              }),\n              margin: \"normal\",\n              InputLabelProps: {\n                shrink: true\n              },\n              disabled: dialogMode === 'view'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          children: dialogMode === 'view' ? 'Chiudi' : 'Annulla'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 11\n        }, this), dialogMode !== 'view' && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmit,\n          variant: \"contained\",\n          children: dialogMode === 'create' ? 'Crea' : dialogMode === 'edit' ? 'Salva' : dialogMode === 'assign' ? 'Assegna Cavi' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 196,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandeList, \"wNCS3Fa3vufTV0GS+yY4LklGBDU=\");\n_c = ComandeList;\nexport default ComandeList;\nvar _c;\n$RefreshReg$(_c, \"ComandeList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "MenuItem", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON><PERSON>", "Grid", "List", "ListItem", "ListItemText", "Divider", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Visibility", "ViewIcon", "Assignment", "AssignIcon", "Refresh", "RefreshIcon", "comandeService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ComandeList", "cantiereId", "cantiereName", "_s", "comande", "setComande", "loading", "setLoading", "error", "setError", "openDialog", "setOpenDialog", "selectedComanda", "setSelectedComanda", "dialogMode", "setDialogMode", "formData", "setFormData", "tipo_comanda", "descrizione", "responsabile", "data_scadenza", "statistiche", "setStatistiche", "caviAssegnazione", "setCaviAssegnazione", "loadComande", "loadStatistiche", "response", "getComande", "err", "console", "stats", "getStatisticheComande", "handleOpenDialog", "mode", "comanda", "handleCloseDialog", "handleSubmit", "createComanda", "id_cantiere", "parseInt", "log", "trim", "listaIdCavi", "split", "map", "id", "filter", "assegnaCavi", "codice_comanda", "handleDelete", "codiceComanda", "window", "confirm", "deleteComanda", "getStatoColor", "stato", "getTipoComandaLabel", "tipo", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "mb", "container", "spacing", "item", "xs", "sm", "md", "color", "gutterBottom", "variant", "totale_comande", "comande_create", "comande_assegnate", "comande_in_corso", "comande_completate", "percentuale_completamento_medio", "toFixed", "flexWrap", "gap", "startIcon", "onClick", "length", "disabled", "severity", "sx", "component", "fontWeight", "label", "size", "Date", "data_creazione", "toLocaleDateString", "numero_cavi_assegnati", "percentuale_completamento", "title", "textAlign", "py", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "pt", "value", "onChange", "e", "target", "margin", "placeholder", "helperText", "multiline", "rows", "select", "type", "InputLabelProps", "shrink", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/ComandeList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Card,\n  CardContent,\n  Ty<PERSON><PERSON>,\n  Button,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  MenuItem,\n  Alert,\n  CircularProgress,\n  Tooltip,\n  Grid,\n  List,\n  ListItem,\n  ListItemText,\n  Divider\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Visibility as ViewIcon,\n  Assignment as AssignIcon,\n  Refresh as RefreshIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\n\nconst ComandeList = ({ cantiereId, cantiereName }) => {\n  const [comande, setComande] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [dialogMode, setDialogMode] = useState('create'); // 'create', 'edit', 'view'\n  const [formData, setFormData] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: ''\n  });\n  const [statistiche, setStatistiche] = useState(null);\n  const [caviAssegnazione, setCaviAssegnazione] = useState('');\n\n  // Carica le comande al mount del componente\n  useEffect(() => {\n    if (cantiereId) {\n      loadComande();\n      loadStatistiche();\n    }\n  }, [cantiereId]);\n\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      const response = await comandeService.getComande(cantiereId);\n      setComande(response.comande || []);\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n\n  const handleOpenDialog = (mode, comanda = null) => {\n    setDialogMode(mode);\n    setSelectedComanda(comanda);\n    \n    if (mode === 'create') {\n      setFormData({\n        tipo_comanda: 'POSA',\n        descrizione: '',\n        responsabile: '',\n        data_scadenza: ''\n      });\n    } else if (mode === 'edit' && comanda) {\n      setFormData({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || ''\n      });\n    }\n    \n    setOpenDialog(true);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setSelectedComanda(null);\n    setCaviAssegnazione('');\n    setFormData({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: ''\n    });\n  };\n\n  const handleSubmit = async () => {\n    try {\n      if (dialogMode === 'create') {\n        await comandeService.createComanda({\n          ...formData,\n          id_cantiere: parseInt(cantiereId)\n        });\n      } else if (dialogMode === 'edit') {\n        // Implementare update quando sarà disponibile l'endpoint\n        console.log('Update non ancora implementato');\n      } else if (dialogMode === 'assign') {\n        // Assegnazione cavi\n        if (!caviAssegnazione.trim()) {\n          setError('Inserisci almeno un ID cavo');\n          return;\n        }\n\n        const listaIdCavi = caviAssegnazione.split(',').map(id => id.trim()).filter(id => id);\n        await comandeService.assegnaCavi(selectedComanda.codice_comanda, listaIdCavi);\n      }\n\n      handleCloseDialog();\n      loadComande();\n      loadStatistiche();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n\n  const handleDelete = async (codiceComanda) => {\n    if (window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      try {\n        await comandeService.deleteComanda(codiceComanda);\n        loadComande();\n        loadStatistiche();\n      } catch (err) {\n        console.error('Errore nell\\'eliminazione:', err);\n        setError('Errore nell\\'eliminazione della comanda');\n      }\n    }\n  };\n\n  const getStatoColor = (stato) => {\n    switch (stato) {\n      case 'CREATA': return 'default';\n      case 'ASSEGNATA': return 'primary';\n      case 'IN_CORSO': return 'warning';\n      case 'COMPLETATA': return 'success';\n      case 'ANNULLATA': return 'error';\n      default: return 'default';\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    switch (tipo) {\n      case 'POSA': return 'Posa';\n      case 'COLLEGAMENTO_PARTENZA': return 'Collegamento Partenza';\n      case 'COLLEGAMENTO_ARRIVO': return 'Collegamento Arrivo';\n      default: return tipo;\n    }\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      {/* Header con statistiche */}\n      <Box mb={3}>\n        \n        {statistiche && (\n          <Grid container spacing={2} mb={2}>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Totale\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.totale_comande}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Create\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_create}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Assegnate\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_assegnate}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    In Corso\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_in_corso}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    Completate\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.comande_completate}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n            <Grid item xs={12} sm={6} md={2}>\n              <Card>\n                <CardContent>\n                  <Typography color=\"textSecondary\" gutterBottom>\n                    % Completamento\n                  </Typography>\n                  <Typography variant=\"h5\">\n                    {statistiche.percentuale_completamento_medio.toFixed(1)}%\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        )}\n      </Box>\n\n      {/* Toolbar */}\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2} flexWrap=\"wrap\" gap={1}>\n        <Box display=\"flex\" gap={1} flexWrap=\"wrap\">\n          <Button\n            variant=\"contained\"\n            startIcon={<AddIcon />}\n            onClick={() => handleOpenDialog('create')}\n          >\n            Nuova Comanda\n          </Button>\n\n          <Button\n            variant=\"outlined\"\n            startIcon={<AssignIcon />}\n            onClick={() => {\n              if (comande.length === 0) {\n                setError('Nessuna comanda disponibile per l\\'assegnazione');\n                return;\n              }\n              // Apri dialog per selezionare comanda\n              setError('Seleziona una comanda dalla tabella e clicca sull\\'icona \"Assegna Cavi\"');\n            }}\n            disabled={comande.length === 0}\n          >\n            Assegna Cavi\n          </Button>\n        </Box>\n\n        <Button\n          variant=\"outlined\"\n          startIcon={<RefreshIcon />}\n          onClick={() => {\n            loadComande();\n            loadStatistiche();\n          }}\n        >\n          Aggiorna\n        </Button>\n      </Box>\n\n      {/* Messaggio di errore */}\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {/* Tabella comande */}\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell>Codice</TableCell>\n              <TableCell>Tipo</TableCell>\n              <TableCell>Descrizione</TableCell>\n              <TableCell>Responsabile</TableCell>\n              <TableCell>Data Creazione</TableCell>\n              <TableCell>Stato</TableCell>\n              <TableCell>Cavi Assegnati</TableCell>\n              <TableCell>Completamento</TableCell>\n              <TableCell>Azioni</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {comande.map((comanda) => (\n              <TableRow key={comanda.codice_comanda}>\n                <TableCell>\n                  <Typography variant=\"body2\" fontWeight=\"bold\">\n                    {comanda.codice_comanda}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Chip \n                    label={getTipoComandaLabel(comanda.tipo_comanda)}\n                    size=\"small\"\n                    variant=\"outlined\"\n                  />\n                </TableCell>\n                <TableCell>{comanda.descrizione}</TableCell>\n                <TableCell>{comanda.responsabile}</TableCell>\n                <TableCell>\n                  {new Date(comanda.data_creazione).toLocaleDateString('it-IT')}\n                </TableCell>\n                <TableCell>\n                  <Chip \n                    label={comanda.stato}\n                    color={getStatoColor(comanda.stato)}\n                    size=\"small\"\n                  />\n                </TableCell>\n                <TableCell>{comanda.numero_cavi_assegnati || 0}</TableCell>\n                <TableCell>\n                  {comanda.percentuale_completamento ? \n                    `${comanda.percentuale_completamento.toFixed(1)}%` : '0%'}\n                </TableCell>\n                <TableCell>\n                  <Tooltip title=\"Visualizza\">\n                    <IconButton \n                      size=\"small\"\n                      onClick={() => handleOpenDialog('view', comanda)}\n                    >\n                      <ViewIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"Modifica\">\n                    <IconButton \n                      size=\"small\"\n                      onClick={() => handleOpenDialog('edit', comanda)}\n                    >\n                      <EditIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"Assegna Cavi\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => handleOpenDialog('assign', comanda)}\n                      color=\"primary\"\n                    >\n                      <AssignIcon />\n                    </IconButton>\n                  </Tooltip>\n                  <Tooltip title=\"Elimina\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => handleDelete(comanda.codice_comanda)}\n                      color=\"error\"\n                    >\n                      <DeleteIcon />\n                    </IconButton>\n                  </Tooltip>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {comande.length === 0 && !loading && (\n        <Box textAlign=\"center\" py={4}>\n          <Typography variant=\"h6\" color=\"textSecondary\">\n            Nessuna comanda trovata\n          </Typography>\n          <Typography variant=\"body2\" color=\"textSecondary\">\n            Clicca su \"Nuova Comanda\" per iniziare\n          </Typography>\n        </Box>\n      )}\n\n      {/* Dialog per creazione/modifica/assegnazione */}\n      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"sm\" fullWidth>\n        <DialogTitle>\n          {dialogMode === 'create' && 'Nuova Comanda'}\n          {dialogMode === 'edit' && 'Modifica Comanda'}\n          {dialogMode === 'view' && 'Dettagli Comanda'}\n          {dialogMode === 'assign' && `Assegna Cavi - ${selectedComanda?.codice_comanda}`}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 1 }}>\n            {dialogMode === 'assign' ? (\n              <>\n                <Alert severity=\"info\" sx={{ mb: 2 }}>\n                  Inserisci gli ID dei cavi da assegnare alla comanda, separati da virgola.\n                </Alert>\n                <TextField\n                  fullWidth\n                  label=\"ID Cavi (separati da virgola)\"\n                  value={caviAssegnazione}\n                  onChange={(e) => setCaviAssegnazione(e.target.value)}\n                  margin=\"normal\"\n                  placeholder=\"es: CAVO001, CAVO002, CAVO003\"\n                  helperText=\"Esempio: CAVO001, CAVO002, CAVO003\"\n                  multiline\n                  rows={3}\n                />\n              </>\n            ) : (\n              <>\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Tipo Comanda\"\n                  value={formData.tipo_comanda}\n                  onChange={(e) => setFormData({ ...formData, tipo_comanda: e.target.value })}\n                  margin=\"normal\"\n                  disabled={dialogMode === 'view'}\n                >\n                  <MenuItem value=\"POSA\">Posa</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_PARTENZA\">Collegamento Partenza</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_ARRIVO\">Collegamento Arrivo</MenuItem>\n                </TextField>\n\n                <TextField\n                  fullWidth\n                  label=\"Descrizione\"\n                  value={formData.descrizione}\n                  onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}\n                  margin=\"normal\"\n                  multiline\n                  rows={3}\n                  disabled={dialogMode === 'view'}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Responsabile\"\n                  value={formData.responsabile}\n                  onChange={(e) => setFormData({ ...formData, responsabile: e.target.value })}\n                  margin=\"normal\"\n                  disabled={dialogMode === 'view'}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Data Scadenza\"\n                  type=\"date\"\n                  value={formData.data_scadenza}\n                  onChange={(e) => setFormData({ ...formData, data_scadenza: e.target.value })}\n                  margin=\"normal\"\n                  InputLabelProps={{ shrink: true }}\n                  disabled={dialogMode === 'view'}\n                />\n              </>\n            )}\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDialog}>\n            {dialogMode === 'view' ? 'Chiudi' : 'Annulla'}\n          </Button>\n          {dialogMode !== 'view' && (\n            <Button onClick={handleSubmit} variant=\"contained\">\n              {dialogMode === 'create' ? 'Crea' :\n               dialogMode === 'edit' ? 'Salva' :\n               dialogMode === 'assign' ? 'Assegna Cavi' : 'Salva'}\n            </Button>\n          )}\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ComandeList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,QAAQ,EACtBC,UAAU,IAAIC,UAAU,EACxBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3D,MAAMC,WAAW,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqD,OAAO,EAAEC,UAAU,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuD,KAAK,EAAEC,QAAQ,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyD,UAAU,EAAEC,aAAa,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC2D,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACxD,MAAM,CAAC+D,QAAQ,EAAEC,WAAW,CAAC,GAAGhE,QAAQ,CAAC;IACvCiE,YAAY,EAAE,MAAM;IACpBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACuE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACAC,SAAS,CAAC,MAAM;IACd,IAAI+C,UAAU,EAAE;MACdyB,WAAW,CAAC,CAAC;MACbC,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAC1B,UAAU,CAAC,CAAC;EAEhB,MAAMyB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqB,QAAQ,GAAG,MAAMjC,cAAc,CAACkC,UAAU,CAAC5B,UAAU,CAAC;MAC5DI,UAAU,CAACuB,QAAQ,CAACxB,OAAO,IAAI,EAAE,CAAC;MAClCK,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOqB,GAAG,EAAE;MACZC,OAAO,CAACvB,KAAK,CAAC,uCAAuC,EAAEsB,GAAG,CAAC;MAC3DrB,QAAQ,CAAC,sCAAsC,CAAC;IAClD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMK,KAAK,GAAG,MAAMrC,cAAc,CAACsC,qBAAqB,CAAChC,UAAU,CAAC;MACpEsB,cAAc,CAACS,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAACvB,KAAK,CAAC,2CAA2C,EAAEsB,GAAG,CAAC;IACjE;EACF,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAACC,IAAI,EAAEC,OAAO,GAAG,IAAI,KAAK;IACjDrB,aAAa,CAACoB,IAAI,CAAC;IACnBtB,kBAAkB,CAACuB,OAAO,CAAC;IAE3B,IAAID,IAAI,KAAK,QAAQ,EAAE;MACrBlB,WAAW,CAAC;QACVC,YAAY,EAAE,MAAM;QACpBC,WAAW,EAAE,EAAE;QACfC,YAAY,EAAE,EAAE;QAChBC,aAAa,EAAE;MACjB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIc,IAAI,KAAK,MAAM,IAAIC,OAAO,EAAE;MACrCnB,WAAW,CAAC;QACVC,YAAY,EAAEkB,OAAO,CAAClB,YAAY;QAClCC,WAAW,EAAEiB,OAAO,CAACjB,WAAW,IAAI,EAAE;QACtCC,YAAY,EAAEgB,OAAO,CAAChB,YAAY,IAAI,EAAE;QACxCC,aAAa,EAAEe,OAAO,CAACf,aAAa,IAAI;MAC1C,CAAC,CAAC;IACJ;IAEAV,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAM0B,iBAAiB,GAAGA,CAAA,KAAM;IAC9B1B,aAAa,CAAC,KAAK,CAAC;IACpBE,kBAAkB,CAAC,IAAI,CAAC;IACxBY,mBAAmB,CAAC,EAAE,CAAC;IACvBR,WAAW,CAAC;MACVC,YAAY,EAAE,MAAM;MACpBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,IAAIxB,UAAU,KAAK,QAAQ,EAAE;QAC3B,MAAMnB,cAAc,CAAC4C,aAAa,CAAC;UACjC,GAAGvB,QAAQ;UACXwB,WAAW,EAAEC,QAAQ,CAACxC,UAAU;QAClC,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIa,UAAU,KAAK,MAAM,EAAE;QAChC;QACAiB,OAAO,CAACW,GAAG,CAAC,gCAAgC,CAAC;MAC/C,CAAC,MAAM,IAAI5B,UAAU,KAAK,QAAQ,EAAE;QAClC;QACA,IAAI,CAACU,gBAAgB,CAACmB,IAAI,CAAC,CAAC,EAAE;UAC5BlC,QAAQ,CAAC,6BAA6B,CAAC;UACvC;QACF;QAEA,MAAMmC,WAAW,GAAGpB,gBAAgB,CAACqB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACJ,IAAI,CAAC,CAAC,CAAC,CAACK,MAAM,CAACD,EAAE,IAAIA,EAAE,CAAC;QACrF,MAAMpD,cAAc,CAACsD,WAAW,CAACrC,eAAe,CAACsC,cAAc,EAAEN,WAAW,CAAC;MAC/E;MAEAP,iBAAiB,CAAC,CAAC;MACnBX,WAAW,CAAC,CAAC;MACbC,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAACvB,KAAK,CAAC,yBAAyB,EAAEsB,GAAG,CAAC;MAC7CrB,QAAQ,CAAC,sCAAsC,CAAC;IAClD;EACF,CAAC;EAED,MAAM0C,YAAY,GAAG,MAAOC,aAAa,IAAK;IAC5C,IAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnE,IAAI;QACF,MAAM3D,cAAc,CAAC4D,aAAa,CAACH,aAAa,CAAC;QACjD1B,WAAW,CAAC,CAAC;QACbC,eAAe,CAAC,CAAC;MACnB,CAAC,CAAC,OAAOG,GAAG,EAAE;QACZC,OAAO,CAACvB,KAAK,CAAC,4BAA4B,EAAEsB,GAAG,CAAC;QAChDrB,QAAQ,CAAC,yCAAyC,CAAC;MACrD;IACF;EACF,CAAC;EAED,MAAM+C,aAAa,GAAIC,KAAK,IAAK;IAC/B,QAAQA,KAAK;MACX,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,YAAY;QAAE,OAAO,SAAS;MACnC,KAAK,WAAW;QAAE,OAAO,OAAO;MAChC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAIC,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,MAAM;QAAE,OAAO,MAAM;MAC1B,KAAK,uBAAuB;QAAE,OAAO,uBAAuB;MAC5D,KAAK,qBAAqB;QAAE,OAAO,qBAAqB;MACxD;QAAS,OAAOA,IAAI;IACtB;EACF,CAAC;EAED,IAAIrD,OAAO,EAAE;IACX,oBACET,OAAA,CAAC1C,GAAG;MAACyG,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/EnE,OAAA,CAACrB,gBAAgB;QAAAyF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACEvE,OAAA,CAAC1C,GAAG;IAAA6G,QAAA,gBAEFnE,OAAA,CAAC1C,GAAG;MAACkH,EAAE,EAAE,CAAE;MAAAL,QAAA,EAER1C,WAAW,iBACVzB,OAAA,CAACnB,IAAI;QAAC4F,SAAS;QAACC,OAAO,EAAE,CAAE;QAACF,EAAE,EAAE,CAAE;QAAAL,QAAA,gBAChCnE,OAAA,CAACnB,IAAI;UAAC8F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BnE,OAAA,CAACzC,IAAI;YAAA4G,QAAA,eACHnE,OAAA,CAACxC,WAAW;cAAA2G,QAAA,gBACVnE,OAAA,CAACvC,UAAU;gBAACsH,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvE,OAAA,CAACvC,UAAU;gBAACwH,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB1C,WAAW,CAACyD;cAAc;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPvE,OAAA,CAACnB,IAAI;UAAC8F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BnE,OAAA,CAACzC,IAAI;YAAA4G,QAAA,eACHnE,OAAA,CAACxC,WAAW;cAAA2G,QAAA,gBACVnE,OAAA,CAACvC,UAAU;gBAACsH,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvE,OAAA,CAACvC,UAAU;gBAACwH,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB1C,WAAW,CAAC0D;cAAc;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPvE,OAAA,CAACnB,IAAI;UAAC8F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BnE,OAAA,CAACzC,IAAI;YAAA4G,QAAA,eACHnE,OAAA,CAACxC,WAAW;cAAA2G,QAAA,gBACVnE,OAAA,CAACvC,UAAU;gBAACsH,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvE,OAAA,CAACvC,UAAU;gBAACwH,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB1C,WAAW,CAAC2D;cAAiB;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPvE,OAAA,CAACnB,IAAI;UAAC8F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BnE,OAAA,CAACzC,IAAI;YAAA4G,QAAA,eACHnE,OAAA,CAACxC,WAAW;cAAA2G,QAAA,gBACVnE,OAAA,CAACvC,UAAU;gBAACsH,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvE,OAAA,CAACvC,UAAU;gBAACwH,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB1C,WAAW,CAAC4D;cAAgB;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPvE,OAAA,CAACnB,IAAI;UAAC8F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BnE,OAAA,CAACzC,IAAI;YAAA4G,QAAA,eACHnE,OAAA,CAACxC,WAAW;cAAA2G,QAAA,gBACVnE,OAAA,CAACvC,UAAU;gBAACsH,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvE,OAAA,CAACvC,UAAU;gBAACwH,OAAO,EAAC,IAAI;gBAAAd,QAAA,EACrB1C,WAAW,CAAC6D;cAAkB;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACPvE,OAAA,CAACnB,IAAI;UAAC8F,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAX,QAAA,eAC9BnE,OAAA,CAACzC,IAAI;YAAA4G,QAAA,eACHnE,OAAA,CAACxC,WAAW;cAAA2G,QAAA,gBACVnE,OAAA,CAACvC,UAAU;gBAACsH,KAAK,EAAC,eAAe;gBAACC,YAAY;gBAAAb,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbvE,OAAA,CAACvC,UAAU;gBAACwH,OAAO,EAAC,IAAI;gBAAAd,QAAA,GACrB1C,WAAW,CAAC8D,+BAA+B,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,GAC1D;cAAA;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACP;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNvE,OAAA,CAAC1C,GAAG;MAACyG,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACO,EAAE,EAAE,CAAE;MAACiB,QAAQ,EAAC,MAAM;MAACC,GAAG,EAAE,CAAE;MAAAvB,QAAA,gBACnGnE,OAAA,CAAC1C,GAAG;QAACyG,OAAO,EAAC,MAAM;QAAC2B,GAAG,EAAE,CAAE;QAACD,QAAQ,EAAC,MAAM;QAAAtB,QAAA,gBACzCnE,OAAA,CAACtC,MAAM;UACLuH,OAAO,EAAC,WAAW;UACnBU,SAAS,eAAE3F,OAAA,CAACb,OAAO;YAAAiF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBqB,OAAO,EAAEA,CAAA,KAAMvD,gBAAgB,CAAC,QAAQ,CAAE;UAAA8B,QAAA,EAC3C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETvE,OAAA,CAACtC,MAAM;UACLuH,OAAO,EAAC,UAAU;UAClBU,SAAS,eAAE3F,OAAA,CAACL,UAAU;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC1BqB,OAAO,EAAEA,CAAA,KAAM;YACb,IAAIrF,OAAO,CAACsF,MAAM,KAAK,CAAC,EAAE;cACxBjF,QAAQ,CAAC,iDAAiD,CAAC;cAC3D;YACF;YACA;YACAA,QAAQ,CAAC,yEAAyE,CAAC;UACrF,CAAE;UACFkF,QAAQ,EAAEvF,OAAO,CAACsF,MAAM,KAAK,CAAE;UAAA1B,QAAA,EAChC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENvE,OAAA,CAACtC,MAAM;QACLuH,OAAO,EAAC,UAAU;QAClBU,SAAS,eAAE3F,OAAA,CAACH,WAAW;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3BqB,OAAO,EAAEA,CAAA,KAAM;UACb/D,WAAW,CAAC,CAAC;UACbC,eAAe,CAAC,CAAC;QACnB,CAAE;QAAAqC,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAGL5D,KAAK,iBACJX,OAAA,CAACtB,KAAK;MAACqH,QAAQ,EAAC,OAAO;MAACC,EAAE,EAAE;QAAExB,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,EACnCxD;IAAK;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAGDvE,OAAA,CAAClC,cAAc;MAACmI,SAAS,EAAEhI,KAAM;MAAAkG,QAAA,eAC/BnE,OAAA,CAACrC,KAAK;QAAAwG,QAAA,gBACJnE,OAAA,CAACjC,SAAS;UAAAoG,QAAA,eACRnE,OAAA,CAAChC,QAAQ;YAAAmG,QAAA,gBACPnE,OAAA,CAACnC,SAAS;cAAAsG,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7BvE,OAAA,CAACnC,SAAS;cAAAsG,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3BvE,OAAA,CAACnC,SAAS;cAAAsG,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClCvE,OAAA,CAACnC,SAAS;cAAAsG,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACnCvE,OAAA,CAACnC,SAAS;cAAAsG,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACrCvE,OAAA,CAACnC,SAAS;cAAAsG,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5BvE,OAAA,CAACnC,SAAS;cAAAsG,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACrCvE,OAAA,CAACnC,SAAS;cAAAsG,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACpCvE,OAAA,CAACnC,SAAS;cAAAsG,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZvE,OAAA,CAACpC,SAAS;UAAAuG,QAAA,EACP5D,OAAO,CAAC0C,GAAG,CAAEV,OAAO,iBACnBvC,OAAA,CAAChC,QAAQ;YAAAmG,QAAA,gBACPnE,OAAA,CAACnC,SAAS;cAAAsG,QAAA,eACRnE,OAAA,CAACvC,UAAU;gBAACwH,OAAO,EAAC,OAAO;gBAACiB,UAAU,EAAC,MAAM;gBAAA/B,QAAA,EAC1C5B,OAAO,CAACc;cAAc;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZvE,OAAA,CAACnC,SAAS;cAAAsG,QAAA,eACRnE,OAAA,CAAC9B,IAAI;gBACHiI,KAAK,EAAEtC,mBAAmB,CAACtB,OAAO,CAAClB,YAAY,CAAE;gBACjD+E,IAAI,EAAC,OAAO;gBACZnB,OAAO,EAAC;cAAU;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZvE,OAAA,CAACnC,SAAS;cAAAsG,QAAA,EAAE5B,OAAO,CAACjB;YAAW;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC5CvE,OAAA,CAACnC,SAAS;cAAAsG,QAAA,EAAE5B,OAAO,CAAChB;YAAY;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7CvE,OAAA,CAACnC,SAAS;cAAAsG,QAAA,EACP,IAAIkC,IAAI,CAAC9D,OAAO,CAAC+D,cAAc,CAAC,CAACC,kBAAkB,CAAC,OAAO;YAAC;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACZvE,OAAA,CAACnC,SAAS;cAAAsG,QAAA,eACRnE,OAAA,CAAC9B,IAAI;gBACHiI,KAAK,EAAE5D,OAAO,CAACqB,KAAM;gBACrBmB,KAAK,EAAEpB,aAAa,CAACpB,OAAO,CAACqB,KAAK,CAAE;gBACpCwC,IAAI,EAAC;cAAO;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZvE,OAAA,CAACnC,SAAS;cAAAsG,QAAA,EAAE5B,OAAO,CAACiE,qBAAqB,IAAI;YAAC;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3DvE,OAAA,CAACnC,SAAS;cAAAsG,QAAA,EACP5B,OAAO,CAACkE,yBAAyB,GAChC,GAAGlE,OAAO,CAACkE,yBAAyB,CAACjB,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;YAAI;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACZvE,OAAA,CAACnC,SAAS;cAAAsG,QAAA,gBACRnE,OAAA,CAACpB,OAAO;gBAAC8H,KAAK,EAAC,YAAY;gBAAAvC,QAAA,eACzBnE,OAAA,CAAC7B,UAAU;kBACTiI,IAAI,EAAC,OAAO;kBACZR,OAAO,EAAEA,CAAA,KAAMvD,gBAAgB,CAAC,MAAM,EAAEE,OAAO,CAAE;kBAAA4B,QAAA,eAEjDnE,OAAA,CAACP,QAAQ;oBAAA2E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACVvE,OAAA,CAACpB,OAAO;gBAAC8H,KAAK,EAAC,UAAU;gBAAAvC,QAAA,eACvBnE,OAAA,CAAC7B,UAAU;kBACTiI,IAAI,EAAC,OAAO;kBACZR,OAAO,EAAEA,CAAA,KAAMvD,gBAAgB,CAAC,MAAM,EAAEE,OAAO,CAAE;kBAAA4B,QAAA,eAEjDnE,OAAA,CAACX,QAAQ;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACVvE,OAAA,CAACpB,OAAO;gBAAC8H,KAAK,EAAC,cAAc;gBAAAvC,QAAA,eAC3BnE,OAAA,CAAC7B,UAAU;kBACTiI,IAAI,EAAC,OAAO;kBACZR,OAAO,EAAEA,CAAA,KAAMvD,gBAAgB,CAAC,QAAQ,EAAEE,OAAO,CAAE;kBACnDwC,KAAK,EAAC,SAAS;kBAAAZ,QAAA,eAEfnE,OAAA,CAACL,UAAU;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACVvE,OAAA,CAACpB,OAAO;gBAAC8H,KAAK,EAAC,SAAS;gBAAAvC,QAAA,eACtBnE,OAAA,CAAC7B,UAAU;kBACTiI,IAAI,EAAC,OAAO;kBACZR,OAAO,EAAEA,CAAA,KAAMtC,YAAY,CAACf,OAAO,CAACc,cAAc,CAAE;kBACpD0B,KAAK,EAAC,OAAO;kBAAAZ,QAAA,eAEbnE,OAAA,CAACT,UAAU;oBAAA6E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA,GAjEChC,OAAO,CAACc,cAAc;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkE3B,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,EAEhBhE,OAAO,CAACsF,MAAM,KAAK,CAAC,IAAI,CAACpF,OAAO,iBAC/BT,OAAA,CAAC1C,GAAG;MAACqJ,SAAS,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAAzC,QAAA,gBAC5BnE,OAAA,CAACvC,UAAU;QAACwH,OAAO,EAAC,IAAI;QAACF,KAAK,EAAC,eAAe;QAAAZ,QAAA,EAAC;MAE/C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbvE,OAAA,CAACvC,UAAU;QAACwH,OAAO,EAAC,OAAO;QAACF,KAAK,EAAC,eAAe;QAAAZ,QAAA,EAAC;MAElD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,eAGDvE,OAAA,CAAC5B,MAAM;MAACyI,IAAI,EAAEhG,UAAW;MAACiG,OAAO,EAAEtE,iBAAkB;MAACuE,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAA7C,QAAA,gBAC3EnE,OAAA,CAAC3B,WAAW;QAAA8F,QAAA,GACTlD,UAAU,KAAK,QAAQ,IAAI,eAAe,EAC1CA,UAAU,KAAK,MAAM,IAAI,kBAAkB,EAC3CA,UAAU,KAAK,MAAM,IAAI,kBAAkB,EAC3CA,UAAU,KAAK,QAAQ,IAAI,kBAAkBF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEsC,cAAc,EAAE;MAAA;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC,eACdvE,OAAA,CAAC1B,aAAa;QAAA6F,QAAA,eACZnE,OAAA,CAAC1C,GAAG;UAAC0I,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAE,CAAE;UAAA9C,QAAA,EAChBlD,UAAU,KAAK,QAAQ,gBACtBjB,OAAA,CAAAE,SAAA;YAAAiE,QAAA,gBACEnE,OAAA,CAACtB,KAAK;cAACqH,QAAQ,EAAC,MAAM;cAACC,EAAE,EAAE;gBAAExB,EAAE,EAAE;cAAE,CAAE;cAAAL,QAAA,EAAC;YAEtC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRvE,OAAA,CAACxB,SAAS;cACRwI,SAAS;cACTb,KAAK,EAAC,+BAA+B;cACrCe,KAAK,EAAEvF,gBAAiB;cACxBwF,QAAQ,EAAGC,CAAC,IAAKxF,mBAAmB,CAACwF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cACrDI,MAAM,EAAC,QAAQ;cACfC,WAAW,EAAC,+BAA+B;cAC3CC,UAAU,EAAC,oCAAoC;cAC/CC,SAAS;cACTC,IAAI,EAAE;YAAE;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA,eACF,CAAC,gBAEHvE,OAAA,CAAAE,SAAA;YAAAiE,QAAA,gBACEnE,OAAA,CAACxB,SAAS;cACRwI,SAAS;cACTW,MAAM;cACNxB,KAAK,EAAC,cAAc;cACpBe,KAAK,EAAE/F,QAAQ,CAACE,YAAa;cAC7B8F,QAAQ,EAAGC,CAAC,IAAKhG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,YAAY,EAAE+F,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC5EI,MAAM,EAAC,QAAQ;cACfxB,QAAQ,EAAE7E,UAAU,KAAK,MAAO;cAAAkD,QAAA,gBAEhCnE,OAAA,CAACvB,QAAQ;gBAACyI,KAAK,EAAC,MAAM;gBAAA/C,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtCvE,OAAA,CAACvB,QAAQ;gBAACyI,KAAK,EAAC,uBAAuB;gBAAA/C,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxEvE,OAAA,CAACvB,QAAQ;gBAACyI,KAAK,EAAC,qBAAqB;gBAAA/C,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eAEZvE,OAAA,CAACxB,SAAS;cACRwI,SAAS;cACTb,KAAK,EAAC,aAAa;cACnBe,KAAK,EAAE/F,QAAQ,CAACG,WAAY;cAC5B6F,QAAQ,EAAGC,CAAC,IAAKhG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,WAAW,EAAE8F,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC3EI,MAAM,EAAC,QAAQ;cACfG,SAAS;cACTC,IAAI,EAAE,CAAE;cACR5B,QAAQ,EAAE7E,UAAU,KAAK;YAAO;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eAEFvE,OAAA,CAACxB,SAAS;cACRwI,SAAS;cACTb,KAAK,EAAC,cAAc;cACpBe,KAAK,EAAE/F,QAAQ,CAACI,YAAa;cAC7B4F,QAAQ,EAAGC,CAAC,IAAKhG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEI,YAAY,EAAE6F,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC5EI,MAAM,EAAC,QAAQ;cACfxB,QAAQ,EAAE7E,UAAU,KAAK;YAAO;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eAEFvE,OAAA,CAACxB,SAAS;cACRwI,SAAS;cACTb,KAAK,EAAC,eAAe;cACrByB,IAAI,EAAC,MAAM;cACXV,KAAK,EAAE/F,QAAQ,CAACK,aAAc;cAC9B2F,QAAQ,EAAGC,CAAC,IAAKhG,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEK,aAAa,EAAE4F,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cAC7EI,MAAM,EAAC,QAAQ;cACfO,eAAe,EAAE;gBAAEC,MAAM,EAAE;cAAK,CAAE;cAClChC,QAAQ,EAAE7E,UAAU,KAAK;YAAO;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA,eACF;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBvE,OAAA,CAACzB,aAAa;QAAA4F,QAAA,gBACZnE,OAAA,CAACtC,MAAM;UAACkI,OAAO,EAAEpD,iBAAkB;UAAA2B,QAAA,EAChClD,UAAU,KAAK,MAAM,GAAG,QAAQ,GAAG;QAAS;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,EACRtD,UAAU,KAAK,MAAM,iBACpBjB,OAAA,CAACtC,MAAM;UAACkI,OAAO,EAAEnD,YAAa;UAACwC,OAAO,EAAC,WAAW;UAAAd,QAAA,EAC/ClD,UAAU,KAAK,QAAQ,GAAG,MAAM,GAChCA,UAAU,KAAK,MAAM,GAAG,OAAO,GAC/BA,UAAU,KAAK,QAAQ,GAAG,cAAc,GAAG;QAAO;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACjE,EAAA,CA5dIH,WAAW;AAAA4H,EAAA,GAAX5H,WAAW;AA8djB,eAAeA,WAAW;AAAC,IAAA4H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}