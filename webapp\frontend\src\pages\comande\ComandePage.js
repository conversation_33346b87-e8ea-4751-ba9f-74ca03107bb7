import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Breadcrumbs,
  Link,
  Alert
} from '@mui/material';
import {
  Home as HomeIcon,
  Assignment as ComandaIcon
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import ComandeList from '../../components/comande/ComandeList';
import { useAuth } from '../../context/AuthContext';

const ComandePage = () => {
  const { cantiereId } = useParams();
  const navigate = useNavigate();
  const { user, isImpersonating, impersonatedUser } = useAuth();
  const [cantiereName, setCantiereName] = useState('');
  const [error, setError] = useState(null);

  // Determina l'utente effettivo (impersonato o normale)
  const effectiveUser = isImpersonating ? impersonatedUser : user;

  useEffect(() => {
    // Verifica che l'utente abbia accesso al cantiere
    if (!cantiereId) {
      setError('ID cantiere non specificato');
      return;
    }

    // Carica il nome del cantiere (potresti voler fare una chiamata API qui)
    // Per ora uso un placeholder
    setCantiereName(`Cantiere ${cantiereId}`);
  }, [cantiereId]);

  const handleBreadcrumbClick = (path) => {
    navigate(path);
  };

  if (error) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ mt: 4 }}>
          <Alert severity="error">{error}</Alert>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        {/* Breadcrumbs */}
        <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
          <Link
            color="inherit"
            href="#"
            onClick={() => handleBreadcrumbClick('/')}
            sx={{ display: 'flex', alignItems: 'center', textDecoration: 'none' }}
          >
            <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Home
          </Link>
          <Link
            color="inherit"
            href="#"
            onClick={() => handleBreadcrumbClick(`/cantieri/${cantiereId}`)}
            sx={{ textDecoration: 'none' }}
          >
            {cantiereName}
          </Link>
          <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
            <ComandaIcon sx={{ mr: 0.5 }} fontSize="inherit" />
            Comande
          </Typography>
        </Breadcrumbs>

        {/* Informazioni utente se impersonato */}
        {isImpersonating && (
          <Alert severity="info" sx={{ mb: 2 }}>
            Stai visualizzando come: {effectiveUser?.username} ({effectiveUser?.role})
          </Alert>
        )}

        {/* Componente principale */}
        <ComandeList 
          cantiereId={cantiereId} 
          cantiereName={cantiereName}
        />
      </Box>
    </Container>
  );
};

export default ComandePage;
