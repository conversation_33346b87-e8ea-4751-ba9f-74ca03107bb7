import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Grid,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  Divider,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Edit as EditIcon,
  Assignment as AssignIcon,
  Build as BuildIcon,
  Link as LinkIcon,
  CheckCircle as CompleteIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import comandeService from '../../services/comandeService';

const ComandaDettagli = ({ codiceComanda, onClose }) => {
  const [comanda, setComanda] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openAssignDialog, setOpenAssignDialog] = useState(false);
  const [openStatusDialog, setOpenStatusDialog] = useState(false);
  const [selectedCavi, setSelectedCavi] = useState([]);
  const [nuovoStato, setNuovoStato] = useState('');

  useEffect(() => {
    if (codiceComanda) {
      loadComandaDettagli();
    }
  }, [codiceComanda]);

  const loadComandaDettagli = async () => {
    try {
      setLoading(true);
      const response = await comandeService.getDettagliComanda(codiceComanda);
      setComanda(response);
      setError(null);
    } catch (err) {
      console.error('Errore nel caricamento dei dettagli:', err);
      setError('Errore nel caricamento dei dettagli della comanda');
    } finally {
      setLoading(false);
    }
  };

  const handleAssegnaCavi = async () => {
    try {
      if (selectedCavi.length === 0) {
        setError('Seleziona almeno un cavo');
        return;
      }

      await comandeService.assegnaCavi(codiceComanda, selectedCavi);
      setOpenAssignDialog(false);
      setSelectedCavi([]);
      loadComandaDettagli();
    } catch (err) {
      console.error('Errore nell\'assegnazione cavi:', err);
      setError('Errore nell\'assegnazione dei cavi');
    }
  };

  const handleCambiaStato = async () => {
    try {
      if (!nuovoStato) {
        setError('Seleziona un nuovo stato');
        return;
      }

      await comandeService.cambiaStato(codiceComanda, nuovoStato);
      setOpenStatusDialog(false);
      setNuovoStato('');
      loadComandaDettagli();
    } catch (err) {
      console.error('Errore nel cambio stato:', err);
      setError('Errore nel cambio di stato');
    }
  };

  const getStatoColor = (stato) => {
    switch (stato) {
      case 'CREATA': return 'default';
      case 'ASSEGNATA': return 'primary';
      case 'IN_CORSO': return 'warning';
      case 'COMPLETATA': return 'success';
      case 'ANNULLATA': return 'error';
      default: return 'default';
    }
  };

  const getTipoComandaLabel = (tipo) => {
    switch (tipo) {
      case 'POSA': return 'Posa';
      case 'COLLEGAMENTO_PARTENZA': return 'Collegamento Partenza';
      case 'COLLEGAMENTO_ARRIVO': return 'Collegamento Arrivo';
      default: return tipo;
    }
  };

  const getStatoIcon = (stato) => {
    switch (stato) {
      case 'COMPLETATA': return <CompleteIcon color="success" />;
      case 'ANNULLATA': return <CancelIcon color="error" />;
      default: return null;
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (!comanda) {
    return (
      <Alert severity="error">
        Comanda non trovata
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Dettagli Comanda: {comanda.codice_comanda}
        </Typography>
        <Button variant="outlined" onClick={onClose}>
          Chiudi
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Informazioni principali */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Informazioni Generali
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText 
                    primary="Tipo Comanda" 
                    secondary={getTipoComandaLabel(comanda.tipo_comanda)} 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Stato" 
                    secondary={
                      <Box display="flex" alignItems="center" gap={1}>
                        <Chip 
                          label={comanda.stato}
                          color={getStatoColor(comanda.stato)}
                          size="small"
                        />
                        {getStatoIcon(comanda.stato)}
                      </Box>
                    }
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Descrizione" 
                    secondary={comanda.descrizione || 'Nessuna descrizione'} 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Responsabile" 
                    secondary={comanda.responsabile || 'Non assegnato'} 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Data Creazione" 
                    secondary={new Date(comanda.data_creazione).toLocaleDateString('it-IT')} 
                  />
                </ListItem>
                {comanda.data_scadenza && (
                  <ListItem>
                    <ListItemText 
                      primary="Data Scadenza" 
                      secondary={new Date(comanda.data_scadenza).toLocaleDateString('it-IT')} 
                    />
                  </ListItem>
                )}
                {comanda.data_completamento && (
                  <ListItem>
                    <ListItemText 
                      primary="Data Completamento" 
                      secondary={new Date(comanda.data_completamento).toLocaleDateString('it-IT')} 
                    />
                  </ListItem>
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Statistiche
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText 
                    primary="Cavi Assegnati" 
                    secondary={comanda.numero_cavi_assegnati || 0} 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Cavi Completati" 
                    secondary={comanda.cavi_completati || 0} 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Percentuale Completamento" 
                    secondary={`${(comanda.percentuale_completamento || 0).toFixed(1)}%`} 
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Azioni */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Azioni
          </Typography>
          <Box display="flex" gap={2} flexWrap="wrap">
            <Button
              variant="contained"
              startIcon={<AssignIcon />}
              onClick={() => setOpenAssignDialog(true)}
              disabled={comanda.stato === 'COMPLETATA' || comanda.stato === 'ANNULLATA'}
            >
              Assegna Cavi
            </Button>
            
            <Button
              variant="outlined"
              startIcon={<EditIcon />}
              onClick={() => setOpenStatusDialog(true)}
              disabled={comanda.stato === 'COMPLETATA' || comanda.stato === 'ANNULLATA'}
            >
              Cambia Stato
            </Button>

            {comanda.tipo_comanda === 'POSA' && (
              <Button
                variant="outlined"
                startIcon={<BuildIcon />}
                disabled={comanda.stato !== 'ASSEGNATA'}
              >
                Aggiorna Posa
              </Button>
            )}

            {(comanda.tipo_comanda === 'COLLEGAMENTO_PARTENZA' || 
              comanda.tipo_comanda === 'COLLEGAMENTO_ARRIVO') && (
              <Button
                variant="outlined"
                startIcon={<LinkIcon />}
                disabled={comanda.stato !== 'ASSEGNATA'}
              >
                Aggiorna Collegamento
              </Button>
            )}
          </Box>
        </CardContent>
      </Card>

      {/* Cavi assegnati */}
      {comanda.cavi && comanda.cavi.length > 0 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Cavi Assegnati ({comanda.cavi.length})
            </Typography>
            <TableContainer component={Paper} variant="outlined">
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>ID Cavo</TableCell>
                    <TableCell>Tipologia</TableCell>
                    <TableCell>Partenza</TableCell>
                    <TableCell>Arrivo</TableCell>
                    <TableCell>Stato</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {comanda.cavi.map((cavo, index) => (
                    <TableRow key={index}>
                      <TableCell>{cavo.id_cavo}</TableCell>
                      <TableCell>{cavo.tipologia}</TableCell>
                      <TableCell>{cavo.partenza}</TableCell>
                      <TableCell>{cavo.arrivo}</TableCell>
                      <TableCell>
                        <Chip 
                          label={cavo.stato_installazione || 'Non installato'}
                          size="small"
                          color={cavo.stato_installazione === 'Installato' ? 'success' : 'default'}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}

      {/* Dialog per assegnazione cavi */}
      <Dialog open={openAssignDialog} onClose={() => setOpenAssignDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Assegna Cavi alla Comanda</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="ID Cavi (separati da virgola)"
            value={selectedCavi.join(', ')}
            onChange={(e) => setSelectedCavi(e.target.value.split(',').map(id => id.trim()).filter(id => id))}
            margin="normal"
            helperText="Inserisci gli ID dei cavi separati da virgola (es: CAVO001, CAVO002)"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenAssignDialog(false)}>Annulla</Button>
          <Button onClick={handleAssegnaCavi} variant="contained">Assegna</Button>
        </DialogActions>
      </Dialog>

      {/* Dialog per cambio stato */}
      <Dialog open={openStatusDialog} onClose={() => setOpenStatusDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Cambia Stato Comanda</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            select
            label="Nuovo Stato"
            value={nuovoStato}
            onChange={(e) => setNuovoStato(e.target.value)}
            margin="normal"
            SelectProps={{ native: true }}
          >
            <option value="">Seleziona stato</option>
            <option value="CREATA">Creata</option>
            <option value="ASSEGNATA">Assegnata</option>
            <option value="IN_CORSO">In Corso</option>
            <option value="COMPLETATA">Completata</option>
            <option value="ANNULLATA">Annullata</option>
          </TextField>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenStatusDialog(false)}>Annulla</Button>
          <Button onClick={handleCambiaStato} variant="contained">Cambia</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ComandaDettagli;
